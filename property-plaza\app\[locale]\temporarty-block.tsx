"use client"
import Image from "next/image";
import PhoneBackground from "@/public/assets/background/phone.png"
import DesktopBackground from "@/public/assets/background/desktop.png"
import TabletBackground from "@/public/assets/background/tablet.png"
import { useEffect } from "react";
import { useTranslations } from "next-intl";
export default function TemporaryBlock() {

  const t = useTranslations("universal")
  useEffect(() => {
    // Add class
    document.body.classList.add('overflow-hidden');

    // Optional cleanup: remove class on unmount
    return () => {
      document.body.classList.remove('overflow-hidden');
    };
  }, []); // Empty dependency array means this runs once on mount
  return <section className="fixed top-0 left-0 w-screen h-screen overflow-hidden z-50 isolate">
    <div className="w-full h-full backdrop-blur-sm">
      <div className="w-fit absolute top-1/2 left-1/2 -translate-x-1/2 backdrop-brightness-50 text-white">
        <h1 className="text-5xl z-20 font-semibold text-center max-w-[80%] mx-auto">{t('misc.temporaryBanner.title')}</h1>
        <p className="text-lg text-center">{t('misc.temporaryBanner.description')}</p>
      </div>
    </div>
    <Image fill src={PhoneBackground} alt="temporary phone background" sizes="1080" className="md:hidden -z-10 brightness-50" />
    <Image fill src={TabletBackground} alt="temporary tablet background" sizes="640" className="max-sm:hidden xl:hidden -z-10 brightness-50 " />
    <Image fill src={DesktopBackground} alt="temporary desktop background" sizes="348" className="max-xl:hidden -z-10  brightness-50" />
  </section>
}