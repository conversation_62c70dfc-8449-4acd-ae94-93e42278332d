"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { usePostFavoriteListing } from "@/core/applications/mutations/listing/use-post-favorite-listing"
import { ListingListSeekers } from "@/core/domain/listing/listing-seekers"
import { PostFavoritePropertyDto } from "@/core/infrastructures/listing/dto"
import { ACCESS_TOKEN } from "@/lib/constanta/constant"
import { cn } from "@/lib/utils"
import { useUserStore } from "@/stores/user.store"
import Cookies from "js-cookie"
import { Heart } from "lucide-react"
import SeekerAuthDialog from "../../../(auth)/seekers-auth-dialog"
import { useTranslations } from "next-intl"
import { useToast } from "@/hooks/use-toast"
import Link from "next/link"
import { noLoginPlanUrl, plansUrl } from "@/lib/constanta/route"
interface ListingHeaderProps {
  listing: ListingListSeekers,
  isFavorite?: boolean,
  code: string,
  size?: "large" | "small",
  extraAction?: React.ReactNode,
  updateClientFavorite: (val: boolean) => void
}
export default function ListingHeader({ code, updateClientFavorite, extraAction, isFavorite, size, listing }: ListingHeaderProps) {
  const t = useTranslations("seeker")
  const favoriteListingMutation = usePostFavoriteListing()
  const bearer = Cookies.get(ACCESS_TOKEN)
  const { role, seekers } = useUserStore(state => state)
  const buttonClassName = cn("z-10  rounded-full h-[26px] w-[26px] hover:bg-transparent hover:scale-110 transition-transform duration-100 ease-linear", size == "small" ? "w-[24px] h-[24px]" : "w-[26px] h-[26px]")
  const iconClassName = cn("text-white", size == "small" ? "!w-4 !h-4" : "!w-5 !h-5")
  const { toast } = useToast()

  const handleFavorite = async () => {
    if (!bearer && role !== "SEEKER") return
    if (seekers.accounts.membership === "Free") {
      toast({
        title: t("misc.subscibePropgram.favorite.title"),
        description: <div>
          {t('misc.subscibePropgram.favorite.description')}
          <Button asChild variant={"link"} size={"sm"} className="p-0 text-seekers-primary h-fit w-fit underline">
            <Link href={seekers.email ? plansUrl : noLoginPlanUrl}>{t('cta.subscribe')}</Link>
          </Button>
        </div>
      })
      return
    }
    const data: PostFavoritePropertyDto = {
      code: code,
      is_favorite: !isFavorite
    }
    try {
      updateClientFavorite(!isFavorite)
      await favoriteListingMutation.mutateAsync(data)
    } catch (error: any) {

    }
  }
  return <div className="w-full py-3 px-2.5 pr-3 flex justify-end items-center gap-2">
    {bearer && role == "SEEKER" ?
      <Button
        size={"icon"}
        onClick={(e) => {
          e.stopPropagation()
          handleFavorite()
        }}
        className={buttonClassName}
        variant={"ghost"}>
        <Heart className={iconClassName} fill={isFavorite ? "red" : "#707070"} fillOpacity={isFavorite ? 1 : 0.5} />
      </Button>
      :
      <SeekerAuthDialog customTrigger={<Button
        size={"icon"}
        className={buttonClassName}
        variant={"ghost"}>
        <Heart className={iconClassName} fill={"#707070"} fillOpacity={0.5} />
      </Button>} />
    }
    {extraAction}
  </div >
}