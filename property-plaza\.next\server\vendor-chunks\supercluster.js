"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/supercluster";
exports.ids = ["vendor-chunks/supercluster"];
exports.modules = {

/***/ "(ssr)/./node_modules/supercluster/index.js":
/*!********************************************!*\
  !*** ./node_modules/supercluster/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Supercluster)\n/* harmony export */ });\n/* harmony import */ var kdbush__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kdbush */ \"(ssr)/./node_modules/kdbush/index.js\");\n\n\n\nconst defaultOptions = {\n    minZoom: 0,   // min zoom to generate clusters on\n    maxZoom: 16,  // max zoom level to cluster the points on\n    minPoints: 2, // minimum points to form a cluster\n    radius: 40,   // cluster radius in pixels\n    extent: 512,  // tile extent (radius is calculated relative to it)\n    nodeSize: 64, // size of the KD-tree leaf node, affects performance\n    log: false,   // whether to log timing info\n\n    // whether to generate numeric ids for input features (in vector tiles)\n    generateId: false,\n\n    // a reduce function for calculating custom cluster properties\n    reduce: null, // (accumulated, props) => { accumulated.sum += props.sum; }\n\n    // properties to use for individual points when running the reducer\n    map: props => props // props => ({sum: props.my_value})\n};\n\nconst fround = Math.fround || (tmp => ((x) => { tmp[0] = +x; return tmp[0]; }))(new Float32Array(1));\n\nconst OFFSET_ZOOM = 2;\nconst OFFSET_ID = 3;\nconst OFFSET_PARENT = 4;\nconst OFFSET_NUM = 5;\nconst OFFSET_PROP = 6;\n\nclass Supercluster {\n    constructor(options) {\n        this.options = Object.assign(Object.create(defaultOptions), options);\n        this.trees = new Array(this.options.maxZoom + 1);\n        this.stride = this.options.reduce ? 7 : 6;\n        this.clusterProps = [];\n    }\n\n    load(points) {\n        const {log, minZoom, maxZoom} = this.options;\n\n        if (log) console.time('total time');\n\n        const timerId = `prepare ${  points.length  } points`;\n        if (log) console.time(timerId);\n\n        this.points = points;\n\n        // generate a cluster object for each point and index input points into a KD-tree\n        const data = [];\n\n        for (let i = 0; i < points.length; i++) {\n            const p = points[i];\n            if (!p.geometry) continue;\n\n            const [lng, lat] = p.geometry.coordinates;\n            const x = fround(lngX(lng));\n            const y = fround(latY(lat));\n            // store internal point/cluster data in flat numeric arrays for performance\n            data.push(\n                x, y, // projected point coordinates\n                Infinity, // the last zoom the point was processed at\n                i, // index of the source feature in the original input array\n                -1, // parent cluster id\n                1 // number of points in a cluster\n            );\n            if (this.options.reduce) data.push(0); // noop\n        }\n        let tree = this.trees[maxZoom + 1] = this._createTree(data);\n\n        if (log) console.timeEnd(timerId);\n\n        // cluster points on max zoom, then cluster the results on previous zoom, etc.;\n        // results in a cluster hierarchy across zoom levels\n        for (let z = maxZoom; z >= minZoom; z--) {\n            const now = +Date.now();\n\n            // create a new set of clusters for the zoom and index them with a KD-tree\n            tree = this.trees[z] = this._createTree(this._cluster(tree, z));\n\n            if (log) console.log('z%d: %d clusters in %dms', z, tree.numItems, +Date.now() - now);\n        }\n\n        if (log) console.timeEnd('total time');\n\n        return this;\n    }\n\n    getClusters(bbox, zoom) {\n        let minLng = ((bbox[0] + 180) % 360 + 360) % 360 - 180;\n        const minLat = Math.max(-90, Math.min(90, bbox[1]));\n        let maxLng = bbox[2] === 180 ? 180 : ((bbox[2] + 180) % 360 + 360) % 360 - 180;\n        const maxLat = Math.max(-90, Math.min(90, bbox[3]));\n\n        if (bbox[2] - bbox[0] >= 360) {\n            minLng = -180;\n            maxLng = 180;\n        } else if (minLng > maxLng) {\n            const easternHem = this.getClusters([minLng, minLat, 180, maxLat], zoom);\n            const westernHem = this.getClusters([-180, minLat, maxLng, maxLat], zoom);\n            return easternHem.concat(westernHem);\n        }\n\n        const tree = this.trees[this._limitZoom(zoom)];\n        const ids = tree.range(lngX(minLng), latY(maxLat), lngX(maxLng), latY(minLat));\n        const data = tree.data;\n        const clusters = [];\n        for (const id of ids) {\n            const k = this.stride * id;\n            clusters.push(data[k + OFFSET_NUM] > 1 ? getClusterJSON(data, k, this.clusterProps) : this.points[data[k + OFFSET_ID]]);\n        }\n        return clusters;\n    }\n\n    getChildren(clusterId) {\n        const originId = this._getOriginId(clusterId);\n        const originZoom = this._getOriginZoom(clusterId);\n        const errorMsg = 'No cluster with the specified id.';\n\n        const tree = this.trees[originZoom];\n        if (!tree) throw new Error(errorMsg);\n\n        const data = tree.data;\n        if (originId * this.stride >= data.length) throw new Error(errorMsg);\n\n        const r = this.options.radius / (this.options.extent * Math.pow(2, originZoom - 1));\n        const x = data[originId * this.stride];\n        const y = data[originId * this.stride + 1];\n        const ids = tree.within(x, y, r);\n        const children = [];\n        for (const id of ids) {\n            const k = id * this.stride;\n            if (data[k + OFFSET_PARENT] === clusterId) {\n                children.push(data[k + OFFSET_NUM] > 1 ? getClusterJSON(data, k, this.clusterProps) : this.points[data[k + OFFSET_ID]]);\n            }\n        }\n\n        if (children.length === 0) throw new Error(errorMsg);\n\n        return children;\n    }\n\n    getLeaves(clusterId, limit, offset) {\n        limit = limit || 10;\n        offset = offset || 0;\n\n        const leaves = [];\n        this._appendLeaves(leaves, clusterId, limit, offset, 0);\n\n        return leaves;\n    }\n\n    getTile(z, x, y) {\n        const tree = this.trees[this._limitZoom(z)];\n        const z2 = Math.pow(2, z);\n        const {extent, radius} = this.options;\n        const p = radius / extent;\n        const top = (y - p) / z2;\n        const bottom = (y + 1 + p) / z2;\n\n        const tile = {\n            features: []\n        };\n\n        this._addTileFeatures(\n            tree.range((x - p) / z2, top, (x + 1 + p) / z2, bottom),\n            tree.data, x, y, z2, tile);\n\n        if (x === 0) {\n            this._addTileFeatures(\n                tree.range(1 - p / z2, top, 1, bottom),\n                tree.data, z2, y, z2, tile);\n        }\n        if (x === z2 - 1) {\n            this._addTileFeatures(\n                tree.range(0, top, p / z2, bottom),\n                tree.data, -1, y, z2, tile);\n        }\n\n        return tile.features.length ? tile : null;\n    }\n\n    getClusterExpansionZoom(clusterId) {\n        let expansionZoom = this._getOriginZoom(clusterId) - 1;\n        while (expansionZoom <= this.options.maxZoom) {\n            const children = this.getChildren(clusterId);\n            expansionZoom++;\n            if (children.length !== 1) break;\n            clusterId = children[0].properties.cluster_id;\n        }\n        return expansionZoom;\n    }\n\n    _appendLeaves(result, clusterId, limit, offset, skipped) {\n        const children = this.getChildren(clusterId);\n\n        for (const child of children) {\n            const props = child.properties;\n\n            if (props && props.cluster) {\n                if (skipped + props.point_count <= offset) {\n                    // skip the whole cluster\n                    skipped += props.point_count;\n                } else {\n                    // enter the cluster\n                    skipped = this._appendLeaves(result, props.cluster_id, limit, offset, skipped);\n                    // exit the cluster\n                }\n            } else if (skipped < offset) {\n                // skip a single point\n                skipped++;\n            } else {\n                // add a single point\n                result.push(child);\n            }\n            if (result.length === limit) break;\n        }\n\n        return skipped;\n    }\n\n    _createTree(data) {\n        const tree = new kdbush__WEBPACK_IMPORTED_MODULE_0__[\"default\"](data.length / this.stride | 0, this.options.nodeSize, Float32Array);\n        for (let i = 0; i < data.length; i += this.stride) tree.add(data[i], data[i + 1]);\n        tree.finish();\n        tree.data = data;\n        return tree;\n    }\n\n    _addTileFeatures(ids, data, x, y, z2, tile) {\n        for (const i of ids) {\n            const k = i * this.stride;\n            const isCluster = data[k + OFFSET_NUM] > 1;\n\n            let tags, px, py;\n            if (isCluster) {\n                tags = getClusterProperties(data, k, this.clusterProps);\n                px = data[k];\n                py = data[k + 1];\n            } else {\n                const p = this.points[data[k + OFFSET_ID]];\n                tags = p.properties;\n                const [lng, lat] = p.geometry.coordinates;\n                px = lngX(lng);\n                py = latY(lat);\n            }\n\n            const f = {\n                type: 1,\n                geometry: [[\n                    Math.round(this.options.extent * (px * z2 - x)),\n                    Math.round(this.options.extent * (py * z2 - y))\n                ]],\n                tags\n            };\n\n            // assign id\n            let id;\n            if (isCluster || this.options.generateId) {\n                // optionally generate id for points\n                id = data[k + OFFSET_ID];\n            } else {\n                // keep id if already assigned\n                id = this.points[data[k + OFFSET_ID]].id;\n            }\n\n            if (id !== undefined) f.id = id;\n\n            tile.features.push(f);\n        }\n    }\n\n    _limitZoom(z) {\n        return Math.max(this.options.minZoom, Math.min(Math.floor(+z), this.options.maxZoom + 1));\n    }\n\n    _cluster(tree, zoom) {\n        const {radius, extent, reduce, minPoints} = this.options;\n        const r = radius / (extent * Math.pow(2, zoom));\n        const data = tree.data;\n        const nextData = [];\n        const stride = this.stride;\n\n        // loop through each point\n        for (let i = 0; i < data.length; i += stride) {\n            // if we've already visited the point at this zoom level, skip it\n            if (data[i + OFFSET_ZOOM] <= zoom) continue;\n            data[i + OFFSET_ZOOM] = zoom;\n\n            // find all nearby points\n            const x = data[i];\n            const y = data[i + 1];\n            const neighborIds = tree.within(data[i], data[i + 1], r);\n\n            const numPointsOrigin = data[i + OFFSET_NUM];\n            let numPoints = numPointsOrigin;\n\n            // count the number of points in a potential cluster\n            for (const neighborId of neighborIds) {\n                const k = neighborId * stride;\n                // filter out neighbors that are already processed\n                if (data[k + OFFSET_ZOOM] > zoom) numPoints += data[k + OFFSET_NUM];\n            }\n\n            // if there were neighbors to merge, and there are enough points to form a cluster\n            if (numPoints > numPointsOrigin && numPoints >= minPoints) {\n                let wx = x * numPointsOrigin;\n                let wy = y * numPointsOrigin;\n\n                let clusterProperties;\n                let clusterPropIndex = -1;\n\n                // encode both zoom and point index on which the cluster originated -- offset by total length of features\n                const id = ((i / stride | 0) << 5) + (zoom + 1) + this.points.length;\n\n                for (const neighborId of neighborIds) {\n                    const k = neighborId * stride;\n\n                    if (data[k + OFFSET_ZOOM] <= zoom) continue;\n                    data[k + OFFSET_ZOOM] = zoom; // save the zoom (so it doesn't get processed twice)\n\n                    const numPoints2 = data[k + OFFSET_NUM];\n                    wx += data[k] * numPoints2; // accumulate coordinates for calculating weighted center\n                    wy += data[k + 1] * numPoints2;\n\n                    data[k + OFFSET_PARENT] = id;\n\n                    if (reduce) {\n                        if (!clusterProperties) {\n                            clusterProperties = this._map(data, i, true);\n                            clusterPropIndex = this.clusterProps.length;\n                            this.clusterProps.push(clusterProperties);\n                        }\n                        reduce(clusterProperties, this._map(data, k));\n                    }\n                }\n\n                data[i + OFFSET_PARENT] = id;\n                nextData.push(wx / numPoints, wy / numPoints, Infinity, id, -1, numPoints);\n                if (reduce) nextData.push(clusterPropIndex);\n\n            } else { // left points as unclustered\n                for (let j = 0; j < stride; j++) nextData.push(data[i + j]);\n\n                if (numPoints > 1) {\n                    for (const neighborId of neighborIds) {\n                        const k = neighborId * stride;\n                        if (data[k + OFFSET_ZOOM] <= zoom) continue;\n                        data[k + OFFSET_ZOOM] = zoom;\n                        for (let j = 0; j < stride; j++) nextData.push(data[k + j]);\n                    }\n                }\n            }\n        }\n\n        return nextData;\n    }\n\n    // get index of the point from which the cluster originated\n    _getOriginId(clusterId) {\n        return (clusterId - this.points.length) >> 5;\n    }\n\n    // get zoom of the point from which the cluster originated\n    _getOriginZoom(clusterId) {\n        return (clusterId - this.points.length) % 32;\n    }\n\n    _map(data, i, clone) {\n        if (data[i + OFFSET_NUM] > 1) {\n            const props = this.clusterProps[data[i + OFFSET_PROP]];\n            return clone ? Object.assign({}, props) : props;\n        }\n        const original = this.points[data[i + OFFSET_ID]].properties;\n        const result = this.options.map(original);\n        return clone && result === original ? Object.assign({}, result) : result;\n    }\n}\n\nfunction getClusterJSON(data, i, clusterProps) {\n    return {\n        type: 'Feature',\n        id: data[i + OFFSET_ID],\n        properties: getClusterProperties(data, i, clusterProps),\n        geometry: {\n            type: 'Point',\n            coordinates: [xLng(data[i]), yLat(data[i + 1])]\n        }\n    };\n}\n\nfunction getClusterProperties(data, i, clusterProps) {\n    const count = data[i + OFFSET_NUM];\n    const abbrev =\n        count >= 10000 ? `${Math.round(count / 1000)  }k` :\n        count >= 1000 ? `${Math.round(count / 100) / 10  }k` : count;\n    const propIndex = data[i + OFFSET_PROP];\n    const properties = propIndex === -1 ? {} : Object.assign({}, clusterProps[propIndex]);\n    return Object.assign(properties, {\n        cluster: true,\n        cluster_id: data[i + OFFSET_ID],\n        point_count: count,\n        point_count_abbreviated: abbrev\n    });\n}\n\n// longitude/latitude to spherical mercator in [0..1] range\nfunction lngX(lng) {\n    return lng / 360 + 0.5;\n}\nfunction latY(lat) {\n    const sin = Math.sin(lat * Math.PI / 180);\n    const y = (0.5 - 0.25 * Math.log((1 + sin) / (1 - sin)) / Math.PI);\n    return y < 0 ? 0 : y > 1 ? 1 : y;\n}\n\n// spherical mercator to longitude/latitude\nfunction xLng(x) {\n    return (x - 0.5) * 360;\n}\nfunction yLat(y) {\n    const y2 = (180 - y * 360) * Math.PI / 180;\n    return 360 * Math.atan(Math.exp(y2)) / Math.PI - 90;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/supercluster/index.js\n");

/***/ })

};
;