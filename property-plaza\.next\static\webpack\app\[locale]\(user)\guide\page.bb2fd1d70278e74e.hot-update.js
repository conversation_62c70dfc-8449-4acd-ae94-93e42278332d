"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(user)/guide/page",{

/***/ "(app-pages-browser)/./app/[locale]/(user)/guide/components/guide-value-proposition.tsx":
/*!**************************************************************************!*\
  !*** ./app/[locale]/(user)/guide/components/guide-value-proposition.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GuideValueProposition; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_FileCheck_MapPin_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,FileCheck,MapPin,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_FileCheck_MapPin_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,FileCheck,MapPin,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_FileCheck_MapPin_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,FileCheck,MapPin,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_FileCheck_MapPin_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,FileCheck,MapPin,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction GuideValueProposition() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations)(\"guide\");\n    const benefits = [\n        {\n            icon: _barrel_optimize_names_AlertTriangle_FileCheck_MapPin_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"Avoid Common Scams\",\n            description: \"Learn the red flags and warning signs that indicate fake brokers, fraudulent listings, and rental scams targeting expats.\",\n            color: \"text-red-500 bg-red-50\"\n        },\n        {\n            icon: _barrel_optimize_names_AlertTriangle_FileCheck_MapPin_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"How the Bali Housing Market Really Works\",\n            description: \"Understand the local rental market, seasonal pricing, and the difference between tourist and local rental rates.\",\n            color: \"text-blue-500 bg-blue-50\"\n        },\n        {\n            icon: _barrel_optimize_names_AlertTriangle_FileCheck_MapPin_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"Zoning Explained Simply\",\n            description: \"Navigate Bali's complex zoning laws and understand what areas are legal for long-term rentals and what to avoid.\",\n            color: \"text-seekers-primary bg-seekers-primary-lightest\"\n        },\n        {\n            icon: _barrel_optimize_names_AlertTriangle_FileCheck_MapPin_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Rental Safety Checklist\",\n            description: \"Essential safety checks, legal requirements, and documentation you need before signing any rental agreement.\",\n            color: \"text-purple-500 bg-purple-50\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gradient-to-br from-gray-50 to-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl sm:text-5xl font-bold text-gray-900 mb-6\",\n                            children: \"What's Inside the Guide?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"Everything you need to know to find safe, legal, and fairly-priced housing in Bali. Written by expats, for expats.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\",\n                    children: benefits.map((benefit, index)=>{\n                        const IconComponent = benefit.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 rounded-2xl \".concat(benefit.color, \" flex items-center justify-center mb-6\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-8 w-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 mb-4\",\n                                    children: benefit.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 leading-relaxed\",\n                                    children: benefit.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                        children: \"Why This Guide Exists\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"leading-relaxed\",\n                                                children: \"We've seen too many expats get burned by fake brokers, overpriced rentals, and legal issues that could have been easily avoided.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"leading-relaxed\",\n                                                children: \"This guide compiles years of experience from Property Plaza's team and hundreds of successful expat relocations to Bali.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"leading-relaxed font-semibold text-gray-900\",\n                                                children: \"Don't learn these lessons the hard way. Get the insider knowledge upfront.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-seekers-primary-lightest to-neutral-lightest rounded-2xl p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-seekers-primary rounded-full flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-sm\",\n                                                        children: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900 mb-1\",\n                                                            children: \"Download the Guide\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Get instant access to our comprehensive housing guide\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-seekers-primary rounded-full flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-sm\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900 mb-1\",\n                                                            children: \"Apply the Knowledge\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Use our checklists and tips during your housing search\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-seekers-primary rounded-full flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-sm\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900 mb-1\",\n                                                            children: \"Find Your Perfect Home\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Browse verified listings on Property Plaza with confidence\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-value-proposition.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s(GuideValueProposition, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations\n    ];\n});\n_c = GuideValueProposition;\nvar _c;\n$RefreshReg$(_c, \"GuideValueProposition\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/(user)/guide/components/guide-value-proposition.tsx\n"));

/***/ })

});