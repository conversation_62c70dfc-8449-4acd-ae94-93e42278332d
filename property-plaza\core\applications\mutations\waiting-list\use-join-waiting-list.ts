import { joinWaitingList } from "@/core/infrastructures/waiting-list/api";
import { WaitingListDto } from "@/core/infrastructures/waiting-list/dto";
import { useToast } from "@/hooks/use-toast";
import { useMutationStore } from "@/stores/mutation.store";
import { useMutation } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

export function useJoinWaitingList(){
  const {toast} = useToast()
  const { setMutationResult } = useMutationStore(state => state)
  const t = useTranslations()
  const mutation = useMutation({
    mutationFn: (data:WaitingListDto) => joinWaitingList(data),
    onSuccess: (response) => {
      setMutationResult("SUCCESS")
      toast({
        title: t('waitingList.sucessJoin.title'),
        description: t('waitingList.sucessJoin.description'),
      })
    },
    onError: (error) => {
      const data: any = (error as any).response.data
      toast({
        title: t('misc.foundError'),
        description: data.message,
        variant: "destructive"
      })
      setMutationResult("ERROR")
    }
  })
  return mutation
}