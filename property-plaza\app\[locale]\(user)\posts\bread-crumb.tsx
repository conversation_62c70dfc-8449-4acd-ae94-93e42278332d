import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Breadcrumb, BreadcrumbItem, B<PERSON><PERSON><PERSON>bList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Home } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";

export default function PostBreadCrumb({ title }: { title: string }) {
  const t = useTranslations("seeker")
  return <MainContentLayout className="xl:max-w-screen-lg mx-auto space-y-6 pt-12 max-sm:pt-0">
    <Breadcrumb className="">
      <BreadcrumbList className="space-x-4 sm:gap-0">
        <BreadcrumbItem className="text-seekers-text font-medium text-sm">
          <Link href={"/"} className="flex gap-2.5 items-center">
            <Home className="w-4 h-4" strokeWidth={1} />
            {t('misc.home')}
          </Link>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="text-seekers-text font-medium text-sm w-3 h-fit text-center">
          /
        </BreadcrumbSeparator>
        <BreadcrumbItem className="capitalize text-seekers-text font-medium text-sm">
          {title}
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  </MainContentLayout>
}