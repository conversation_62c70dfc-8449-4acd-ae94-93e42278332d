{"name": "property-plaza", "version": "0.1.0", "private": true, "scripts": {"dev": " next dev", "dev-owner": " next dev -p 3000", "dev-seeker": " next dev -p 3001", "build": "next build", "start": "next start", "check-types": "tsc --noEmit --pretty", "lint": "eslint .", "test": "vitest run", "e2e-tests": "playwright test"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@google-cloud/translate": "^8.5.0", "@googlemaps/markerclusterer": "^2.5.3", "@hookform/resolvers": "^3.9.0", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.7", "@next/bundle-analyzer": "^15.2.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.7", "@sanity/client": "^6.24.1", "@tanstack/react-query": "^5.59.9", "@tanstack/react-table": "^8.20.1", "@vis.gl/react-google-maps": "^1.5.0", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "compressorjs": "^1.2.1", "countries-list": "^3.1.1", "country-flag-icons": "^1.5.13", "date-fns": "^3.6.0", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-react": "^8.5.1", "framer-motion": "^11.18.2", "glob": "^11.0.1", "google-libphonenumber": "^3.2.38", "input-otp": "^1.2.4", "js-cookie": "^3.0.5", "leven": "^4.0.0", "lucide-react": "^0.427.0", "moment": "^2.30.1", "next": "^14.2.15", "next-auth": "^4.24.7", "next-intl": "^3.23.2", "next-recaptcha-v3": "^1.5.2", "next-sanity": "^9.8.30", "next-share": "^0.27.0", "nextjs-toploader": "^3.7.15", "phone": "^3.1.53", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-facebook-pixel": "^1.0.4", "react-google-recaptcha-v3": "^1.11.0", "react-hook-form": "^7.52.2", "react-icons": "^5.3.0", "react-qr-code": "^2.0.15", "react-slider": "^2.0.6", "recharts": "^2.14.1", "sanity": "^3.68.3", "sharp": "^0.33.5", "socket.io-client": "^4.8.0", "sonner": "^2.0.1", "tailwind-merge": "^2.5.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.2", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@playwright/test": "^1.46.0", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@types/google-libphonenumber": "^7.4.30", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.13", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-slider": "^1.3.6", "eslint": "^8", "eslint-config-next": "14.2.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}