import useSeekersSearch from "@/hooks/use-seekers-search"
import { cn } from "@/lib/utils"
import { useSeekerSearchStore } from "@/stores/seeker-search.store"
import { useTranslations } from "next-intl"
import { useRef, useState } from "react"
import { Pop<PERSON>, PopoverAnchor, PopoverContent } from "../../ui/popover"
import { Label } from "../../ui/label"
import { motion } from "framer-motion"
import { Input } from "../../ui/input"
import { X } from "lucide-react"
import { Button } from "../../ui/button"
import LocationSearchContent from "./location-search-content"


export default function LocationSearch({ customTrigger, isUseAnimation = true }: { customTrigger?: React.ReactNode, isUseAnimation?: boolean }) {
  const t = useTranslations("seeker")
  const [openInput, setOpen] = useState(false)
  const { isOpen, setLocationInputFocused, query } = useSeekerSearchStore(state => state)
  const [tempOpen, setTempOpen] = useState<boolean>(true)
  const {
    handleSetQuery, seekersSearch, handleSearch } = useSeekersSearch()

  const inputRef = useRef<HTMLInputElement | null>(null)
  const handleOpen = (open: boolean) => {
    setOpen(open)
    setLocationInputFocused(open)
  }
  return <div className={cn(isUseAnimation ? (isOpen ? "w-full" : "w-fit") : "w-full")} onClick={() => {
    handleOpen(true)
    inputRef.current?.focus()
  }}>
    <Popover open={openInput && tempOpen} onOpenChange={handleOpen}>
      {customTrigger ? <PopoverAnchor asChild>
        {customTrigger}
      </PopoverAnchor> :
        <PopoverAnchor className="w-full px-4" >
          <>
            <Label className="text-xs font-medium text-seekers-text">{t('navbar.search.locationTitle')}</Label>
            <motion.div
              animate={{
                height: !isOpen ? 0 : 20,
                opacity: !isOpen ? 0 : 100,
                width: !isOpen ? 0 : "100%"
              }}
              transition={{ duration: 0.3 }}
              className="flex justify-between"
            >
              <Input ref={inputRef}
                onFocus={e => handleOpen(true)}
                onChange={(e) => {
                  handleSetQuery(e.target.value)
                  setTempOpen(true)
                }}
                value={query}
                placeholder={t('form.placeholder.seekersFindPropertyLocation')}
                onKeyDown={(e) => {
                  e.stopPropagation()
                  if (e.key === "Enter") {
                    handleSearch()
                    setOpen(false)
                  }
                }}
                className="border-0 placeholder:text-seekers-text-lighter focus:outline-none shadow-none focus-visible:ring-0 focus-visible:border-b w-full rounded-none pb-2 !p-0 h-fit" />
              <Button
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation()
                  e.preventDefault()
                  seekersSearch.setQuery("")
                }}
                size={"icon"}
                className={cn("-mt-2", seekersSearch.query.length > 0 ? "" : "hidden")}>
                <X />
              </Button>
            </motion.div>
          </>
        </PopoverAnchor>
      }
      <PopoverContent
        className="w-full border-seekers-text-lighter/20"
        align="start"
        onOpenAutoFocus={(e) => e.preventDefault()}>
        <LocationSearchContent showContent={setTempOpen} />
      </PopoverContent>
    </Popover >
  </div >
}