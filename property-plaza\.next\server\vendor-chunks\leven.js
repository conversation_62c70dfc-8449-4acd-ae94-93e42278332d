"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/leven";
exports.ids = ["vendor-chunks/leven"];
exports.modules = {

/***/ "(ssr)/./node_modules/leven/index.js":
/*!*************************************!*\
  !*** ./node_modules/leven/index.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ leven)\n/* harmony export */ });\nconst array = [];\nconst characterCodeCache = [];\n\nfunction leven(first, second) {\n\tif (first === second) {\n\t\treturn 0;\n\t}\n\n\tconst swap = first;\n\n\t// Swapping the strings if `a` is longer than `b` so we know which one is the\n\t// shortest & which one is the longest\n\tif (first.length > second.length) {\n\t\tfirst = second;\n\t\tsecond = swap;\n\t}\n\n\tlet firstLength = first.length;\n\tlet secondLength = second.length;\n\n\t// Performing suffix trimming:\n\t// We can linearly drop suffix common to both strings since they\n\t// don't increase distance at all\n\t// Note: `~-` is the bitwise way to perform a `- 1` operation\n\twhile (firstLength > 0 && (first.charCodeAt(~-firstLength) === second.charCodeAt(~-secondLength))) {\n\t\tfirstLength--;\n\t\tsecondLength--;\n\t}\n\n\t// Performing prefix trimming\n\t// We can linearly drop prefix common to both strings since they\n\t// don't increase distance at all\n\tlet start = 0;\n\n\twhile (start < firstLength && (first.charCodeAt(start) === second.charCodeAt(start))) {\n\t\tstart++;\n\t}\n\n\tfirstLength -= start;\n\tsecondLength -= start;\n\n\tif (firstLength === 0) {\n\t\treturn secondLength;\n\t}\n\n\tlet bCharacterCode;\n\tlet result;\n\tlet temporary;\n\tlet temporary2;\n\tlet index = 0;\n\tlet index2 = 0;\n\n\twhile (index < firstLength) {\n\t\tcharacterCodeCache[index] = first.charCodeAt(start + index);\n\t\tarray[index] = ++index;\n\t}\n\n\twhile (index2 < secondLength) {\n\t\tbCharacterCode = second.charCodeAt(start + index2);\n\t\ttemporary = index2++;\n\t\tresult = index2;\n\n\t\tfor (index = 0; index < firstLength; index++) {\n\t\t\ttemporary2 = bCharacterCode === characterCodeCache[index] ? temporary : temporary + 1;\n\t\t\ttemporary = array[index];\n\t\t\t// eslint-disable-next-line no-multi-assign\n\t\t\tresult = array[index] = temporary > result ? (temporary2 > result ? result + 1 : temporary2) : (temporary2 > temporary ? temporary + 1 : temporary2);\n\t\t}\n\t}\n\n\treturn result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/leven/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/leven/index.js":
/*!*************************************!*\
  !*** ./node_modules/leven/index.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ leven)\n/* harmony export */ });\nconst array = [];\nconst characterCodeCache = [];\n\nfunction leven(first, second) {\n\tif (first === second) {\n\t\treturn 0;\n\t}\n\n\tconst swap = first;\n\n\t// Swapping the strings if `a` is longer than `b` so we know which one is the\n\t// shortest & which one is the longest\n\tif (first.length > second.length) {\n\t\tfirst = second;\n\t\tsecond = swap;\n\t}\n\n\tlet firstLength = first.length;\n\tlet secondLength = second.length;\n\n\t// Performing suffix trimming:\n\t// We can linearly drop suffix common to both strings since they\n\t// don't increase distance at all\n\t// Note: `~-` is the bitwise way to perform a `- 1` operation\n\twhile (firstLength > 0 && (first.charCodeAt(~-firstLength) === second.charCodeAt(~-secondLength))) {\n\t\tfirstLength--;\n\t\tsecondLength--;\n\t}\n\n\t// Performing prefix trimming\n\t// We can linearly drop prefix common to both strings since they\n\t// don't increase distance at all\n\tlet start = 0;\n\n\twhile (start < firstLength && (first.charCodeAt(start) === second.charCodeAt(start))) {\n\t\tstart++;\n\t}\n\n\tfirstLength -= start;\n\tsecondLength -= start;\n\n\tif (firstLength === 0) {\n\t\treturn secondLength;\n\t}\n\n\tlet bCharacterCode;\n\tlet result;\n\tlet temporary;\n\tlet temporary2;\n\tlet index = 0;\n\tlet index2 = 0;\n\n\twhile (index < firstLength) {\n\t\tcharacterCodeCache[index] = first.charCodeAt(start + index);\n\t\tarray[index] = ++index;\n\t}\n\n\twhile (index2 < secondLength) {\n\t\tbCharacterCode = second.charCodeAt(start + index2);\n\t\ttemporary = index2++;\n\t\tresult = index2;\n\n\t\tfor (index = 0; index < firstLength; index++) {\n\t\t\ttemporary2 = bCharacterCode === characterCodeCache[index] ? temporary : temporary + 1;\n\t\t\ttemporary = array[index];\n\t\t\t// eslint-disable-next-line no-multi-assign\n\t\t\tresult = array[index] = temporary > result ? (temporary2 > result ? result + 1 : temporary2) : (temporary2 > temporary ? temporary + 1 : temporary2);\n\t\t}\n\t}\n\n\treturn result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGV2ZW4vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7O0FBRWU7QUFDZjtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixxQkFBcUI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9sZXZlbi9pbmRleC5qcz9iN2U5Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGFycmF5ID0gW107XG5jb25zdCBjaGFyYWN0ZXJDb2RlQ2FjaGUgPSBbXTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbGV2ZW4oZmlyc3QsIHNlY29uZCkge1xuXHRpZiAoZmlyc3QgPT09IHNlY29uZCkge1xuXHRcdHJldHVybiAwO1xuXHR9XG5cblx0Y29uc3Qgc3dhcCA9IGZpcnN0O1xuXG5cdC8vIFN3YXBwaW5nIHRoZSBzdHJpbmdzIGlmIGBhYCBpcyBsb25nZXIgdGhhbiBgYmAgc28gd2Uga25vdyB3aGljaCBvbmUgaXMgdGhlXG5cdC8vIHNob3J0ZXN0ICYgd2hpY2ggb25lIGlzIHRoZSBsb25nZXN0XG5cdGlmIChmaXJzdC5sZW5ndGggPiBzZWNvbmQubGVuZ3RoKSB7XG5cdFx0Zmlyc3QgPSBzZWNvbmQ7XG5cdFx0c2Vjb25kID0gc3dhcDtcblx0fVxuXG5cdGxldCBmaXJzdExlbmd0aCA9IGZpcnN0Lmxlbmd0aDtcblx0bGV0IHNlY29uZExlbmd0aCA9IHNlY29uZC5sZW5ndGg7XG5cblx0Ly8gUGVyZm9ybWluZyBzdWZmaXggdHJpbW1pbmc6XG5cdC8vIFdlIGNhbiBsaW5lYXJseSBkcm9wIHN1ZmZpeCBjb21tb24gdG8gYm90aCBzdHJpbmdzIHNpbmNlIHRoZXlcblx0Ly8gZG9uJ3QgaW5jcmVhc2UgZGlzdGFuY2UgYXQgYWxsXG5cdC8vIE5vdGU6IGB+LWAgaXMgdGhlIGJpdHdpc2Ugd2F5IHRvIHBlcmZvcm0gYSBgLSAxYCBvcGVyYXRpb25cblx0d2hpbGUgKGZpcnN0TGVuZ3RoID4gMCAmJiAoZmlyc3QuY2hhckNvZGVBdCh+LWZpcnN0TGVuZ3RoKSA9PT0gc2Vjb25kLmNoYXJDb2RlQXQofi1zZWNvbmRMZW5ndGgpKSkge1xuXHRcdGZpcnN0TGVuZ3RoLS07XG5cdFx0c2Vjb25kTGVuZ3RoLS07XG5cdH1cblxuXHQvLyBQZXJmb3JtaW5nIHByZWZpeCB0cmltbWluZ1xuXHQvLyBXZSBjYW4gbGluZWFybHkgZHJvcCBwcmVmaXggY29tbW9uIHRvIGJvdGggc3RyaW5ncyBzaW5jZSB0aGV5XG5cdC8vIGRvbid0IGluY3JlYXNlIGRpc3RhbmNlIGF0IGFsbFxuXHRsZXQgc3RhcnQgPSAwO1xuXG5cdHdoaWxlIChzdGFydCA8IGZpcnN0TGVuZ3RoICYmIChmaXJzdC5jaGFyQ29kZUF0KHN0YXJ0KSA9PT0gc2Vjb25kLmNoYXJDb2RlQXQoc3RhcnQpKSkge1xuXHRcdHN0YXJ0Kys7XG5cdH1cblxuXHRmaXJzdExlbmd0aCAtPSBzdGFydDtcblx0c2Vjb25kTGVuZ3RoIC09IHN0YXJ0O1xuXG5cdGlmIChmaXJzdExlbmd0aCA9PT0gMCkge1xuXHRcdHJldHVybiBzZWNvbmRMZW5ndGg7XG5cdH1cblxuXHRsZXQgYkNoYXJhY3RlckNvZGU7XG5cdGxldCByZXN1bHQ7XG5cdGxldCB0ZW1wb3Jhcnk7XG5cdGxldCB0ZW1wb3JhcnkyO1xuXHRsZXQgaW5kZXggPSAwO1xuXHRsZXQgaW5kZXgyID0gMDtcblxuXHR3aGlsZSAoaW5kZXggPCBmaXJzdExlbmd0aCkge1xuXHRcdGNoYXJhY3RlckNvZGVDYWNoZVtpbmRleF0gPSBmaXJzdC5jaGFyQ29kZUF0KHN0YXJ0ICsgaW5kZXgpO1xuXHRcdGFycmF5W2luZGV4XSA9ICsraW5kZXg7XG5cdH1cblxuXHR3aGlsZSAoaW5kZXgyIDwgc2Vjb25kTGVuZ3RoKSB7XG5cdFx0YkNoYXJhY3RlckNvZGUgPSBzZWNvbmQuY2hhckNvZGVBdChzdGFydCArIGluZGV4Mik7XG5cdFx0dGVtcG9yYXJ5ID0gaW5kZXgyKys7XG5cdFx0cmVzdWx0ID0gaW5kZXgyO1xuXG5cdFx0Zm9yIChpbmRleCA9IDA7IGluZGV4IDwgZmlyc3RMZW5ndGg7IGluZGV4KyspIHtcblx0XHRcdHRlbXBvcmFyeTIgPSBiQ2hhcmFjdGVyQ29kZSA9PT0gY2hhcmFjdGVyQ29kZUNhY2hlW2luZGV4XSA/IHRlbXBvcmFyeSA6IHRlbXBvcmFyeSArIDE7XG5cdFx0XHR0ZW1wb3JhcnkgPSBhcnJheVtpbmRleF07XG5cdFx0XHQvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tbXVsdGktYXNzaWduXG5cdFx0XHRyZXN1bHQgPSBhcnJheVtpbmRleF0gPSB0ZW1wb3JhcnkgPiByZXN1bHQgPyAodGVtcG9yYXJ5MiA+IHJlc3VsdCA/IHJlc3VsdCArIDEgOiB0ZW1wb3JhcnkyKSA6ICh0ZW1wb3JhcnkyID4gdGVtcG9yYXJ5ID8gdGVtcG9yYXJ5ICsgMSA6IHRlbXBvcmFyeTIpO1xuXHRcdH1cblx0fVxuXG5cdHJldHVybiByZXN1bHQ7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/leven/index.js\n");

/***/ })

};
;