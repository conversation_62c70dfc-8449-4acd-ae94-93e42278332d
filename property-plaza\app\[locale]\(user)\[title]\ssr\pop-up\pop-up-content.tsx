import SubscribeDialog from "@/components/subscribe/subscribe-dialog";
import SeekerAuthDialog from "../../../(auth)/seekers-auth-dialog";
import { AUTH_BUTTON_ID, SUBSCRIPTION_BUTTON_ID } from "../utils/use-image-gallery";

export default function PopUpContent() {
  return <>
    <SeekerAuthDialog customTrigger={<button type="button" id={AUTH_BUTTON_ID} className="hidden" />} />
    <SubscribeDialog trigger={<button type="button" id={SUBSCRIPTION_BUTTON_ID} className="hidden" />} />
  </>
}