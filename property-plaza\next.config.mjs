/** @type {import('next').NextConfig} */
import createNextIntlPlugin from "next-intl/plugin";
import bundleAnalyzer from "@next/bundle-analyzer";
const withNextIntl = createNextIntlPlugin("./lib/locale/i18n.ts");
const nextConfig = {
  // compiler: {
  //   removeConsole: true,
  // },
  reactStrictMode: true,
  transpilePackages: ["lucide-react"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "filestorage.property-plaza.id",
        port: "",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "placehold.co",
        port: "",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "images.pexels.com",
        port: "",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "www.facebook.com",
        port: "",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "maps.googleapis.com",
        port: "",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "cdn.sanity.io",
        port: "",
        pathname: "**",
      },
    ],
  },
  productionBrowserSourceMaps: false, // Disable source maps in development
  optimizeFonts: false, // Disable font optimization
  env: {
    PAYMENT_URL: process.env.PAYMENT_URL,
    API_KEY: process.env.API_KEY,
    SUCCESS_RETURN_URL: process.env.SUCCESS_RETURN_URL,
    CANCEL_RETURN_URL: process.env.CANCEL_RETURN_URL,
  },
};
const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
});

export default withNextIntl(withBundleAnalyzer(nextConfig));
