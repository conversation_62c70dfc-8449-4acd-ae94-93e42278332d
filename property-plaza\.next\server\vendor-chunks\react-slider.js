"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-slider";
exports.ids = ["vendor-chunks/react-slider"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-slider/dist/es/dev/components/ReactSlider/ReactSlider.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/react-slider/dist/es/dev/components/ReactSlider/ReactSlider.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReactSlider$1)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/inheritsLoose.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n\n\n\n\n\nvar _jsxFileName = \"/Users/<USER>/github/react-slider/src/components/ReactSlider/ReactSlider.jsx\";\n/**\n * To prevent text selection while dragging.\n * http://stackoverflow.com/questions/5429827/how-can-i-prevent-text-element-selection-with-cursor-drag\n */\n\nfunction pauseEvent(e) {\n  if (e && e.stopPropagation) {\n    e.stopPropagation();\n  }\n\n  if (e && e.preventDefault) {\n    e.preventDefault();\n  }\n\n  return false;\n}\n\nfunction stopPropagation(e) {\n  if (e.stopPropagation) {\n    e.stopPropagation();\n  }\n}\n\nfunction sanitizeInValue(x) {\n  if (x == null) {\n    return [];\n  }\n\n  return Array.isArray(x) ? x.slice() : [x];\n}\n\nfunction prepareOutValue(x) {\n  return x !== null && x.length === 1 ? x[0] : x.slice();\n}\n\nfunction trimSucceeding(length, nextValue, minDistance, max) {\n  for (let i = 0; i < length; i += 1) {\n    const padding = max - i * minDistance;\n\n    if (nextValue[length - 1 - i] > padding) {\n      // eslint-disable-next-line no-param-reassign\n      nextValue[length - 1 - i] = padding;\n    }\n  }\n}\n\nfunction trimPreceding(length, nextValue, minDistance, min) {\n  for (let i = 0; i < length; i += 1) {\n    const padding = min + i * minDistance;\n\n    if (nextValue[i] < padding) {\n      // eslint-disable-next-line no-param-reassign\n      nextValue[i] = padding;\n    }\n  }\n}\n\nfunction addHandlers(eventMap) {\n  Object.keys(eventMap).forEach(key => {\n    if (typeof document !== 'undefined') {\n      document.addEventListener(key, eventMap[key], false);\n    }\n  });\n}\n\nfunction removeHandlers(eventMap) {\n  Object.keys(eventMap).forEach(key => {\n    if (typeof document !== 'undefined') {\n      document.removeEventListener(key, eventMap[key], false);\n    }\n  });\n}\n\nfunction trimAlignValue(val, props) {\n  return alignValue(trimValue(val, props), props);\n}\n\nfunction alignValue(val, props) {\n  const valModStep = (val - props.min) % props.step;\n  let alignedValue = val - valModStep;\n\n  if (Math.abs(valModStep) * 2 >= props.step) {\n    alignedValue += valModStep > 0 ? props.step : -props.step;\n  }\n\n  return parseFloat(alignedValue.toFixed(5));\n}\n\nfunction trimValue(val, props) {\n  let trimmed = val;\n\n  if (trimmed <= props.min) {\n    trimmed = props.min;\n  }\n\n  if (trimmed >= props.max) {\n    trimmed = props.max;\n  }\n\n  return trimmed;\n}\n\nlet ReactSlider = /*#__PURE__*/function (_React$Component) {\n  _babel_runtime_helpers_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__(ReactSlider, _React$Component);\n\n  function ReactSlider(_props) {\n    var _this;\n\n    _this = _React$Component.call(this, _props) || this;\n\n    _this.onKeyUp = () => {\n      _this.onEnd();\n    };\n\n    _this.onMouseUp = () => {\n      _this.onEnd(_this.getMouseEventMap());\n    };\n\n    _this.onTouchEnd = e => {\n      e.preventDefault();\n\n      _this.onEnd(_this.getTouchEventMap());\n    };\n\n    _this.onBlur = () => {\n      _this.setState({\n        index: -1\n      }, _this.onEnd(_this.getKeyDownEventMap()));\n    };\n\n    _this.onMouseMove = e => {\n      // Prevent controlled updates from happening while mouse is moving\n      _this.setState({\n        pending: true\n      });\n\n      const position = _this.getMousePosition(e);\n\n      const diffPosition = _this.getDiffPosition(position[0]);\n\n      const newValue = _this.getValueFromPosition(diffPosition);\n\n      _this.move(newValue);\n    };\n\n    _this.onTouchMove = e => {\n      if (e.touches.length > 1) {\n        return;\n      } // Prevent controlled updates from happending while touch is moving\n\n\n      _this.setState({\n        pending: true\n      });\n\n      const position = _this.getTouchPosition(e);\n\n      if (typeof _this.isScrolling === 'undefined') {\n        const diffMainDir = position[0] - _this.startPosition[0];\n        const diffScrollDir = position[1] - _this.startPosition[1];\n        _this.isScrolling = Math.abs(diffScrollDir) > Math.abs(diffMainDir);\n      }\n\n      if (_this.isScrolling) {\n        _this.setState({\n          index: -1\n        });\n\n        return;\n      }\n\n      const diffPosition = _this.getDiffPosition(position[0]);\n\n      const newValue = _this.getValueFromPosition(diffPosition);\n\n      _this.move(newValue);\n    };\n\n    _this.onKeyDown = e => {\n      if (e.ctrlKey || e.shiftKey || e.altKey || e.metaKey) {\n        return;\n      } // Prevent controlled updates from happening while a key is pressed\n\n\n      _this.setState({\n        pending: true\n      });\n\n      switch (e.key) {\n        case 'ArrowLeft':\n        case 'ArrowDown':\n        case 'Left':\n        case 'Down':\n          e.preventDefault();\n\n          _this.moveDownByStep();\n\n          break;\n\n        case 'ArrowRight':\n        case 'ArrowUp':\n        case 'Right':\n        case 'Up':\n          e.preventDefault();\n\n          _this.moveUpByStep();\n\n          break;\n\n        case 'Home':\n          e.preventDefault();\n\n          _this.move(_this.props.min);\n\n          break;\n\n        case 'End':\n          e.preventDefault();\n\n          _this.move(_this.props.max);\n\n          break;\n\n        case 'PageDown':\n          e.preventDefault();\n\n          _this.moveDownByStep(_this.props.pageFn(_this.props.step));\n\n          break;\n\n        case 'PageUp':\n          e.preventDefault();\n\n          _this.moveUpByStep(_this.props.pageFn(_this.props.step));\n\n          break;\n      }\n    };\n\n    _this.onSliderMouseDown = e => {\n      // do nothing if disabled or right click\n      if (_this.props.disabled || e.button === 2) {\n        return;\n      } // Prevent controlled updates from happening while mouse is moving\n\n\n      _this.setState({\n        pending: true\n      });\n\n      if (!_this.props.snapDragDisabled) {\n        const position = _this.getMousePosition(e);\n\n        _this.forceValueFromPosition(position[0], i => {\n          _this.start(i, position[0]);\n\n          addHandlers(_this.getMouseEventMap());\n        });\n      }\n\n      pauseEvent(e);\n    };\n\n    _this.onSliderClick = e => {\n      if (_this.props.disabled) {\n        return;\n      }\n\n      if (_this.props.onSliderClick && !_this.hasMoved) {\n        const position = _this.getMousePosition(e);\n\n        const valueAtPos = trimAlignValue(_this.calcValue(_this.calcOffsetFromPosition(position[0])), _this.props);\n\n        _this.props.onSliderClick(valueAtPos);\n      }\n    };\n\n    _this.createOnKeyDown = i => e => {\n      if (_this.props.disabled) {\n        return;\n      }\n\n      _this.start(i);\n\n      addHandlers(_this.getKeyDownEventMap());\n      pauseEvent(e);\n    };\n\n    _this.createOnMouseDown = i => e => {\n      // do nothing if disabled or right click\n      if (_this.props.disabled || e.button === 2) {\n        return;\n      } // Prevent controlled updates from happending while mouse is moving\n\n\n      _this.setState({\n        pending: true\n      });\n\n      const position = _this.getMousePosition(e);\n\n      _this.start(i, position[0]);\n\n      addHandlers(_this.getMouseEventMap());\n      pauseEvent(e);\n    };\n\n    _this.createOnTouchStart = i => e => {\n      if (_this.props.disabled || e.touches.length > 1) {\n        return;\n      } // Prevent controlled updates from happending while touch is moving\n\n\n      _this.setState({\n        pending: true\n      });\n\n      const position = _this.getTouchPosition(e);\n\n      _this.startPosition = position; // don't know yet if the user is trying to scroll\n\n      _this.isScrolling = undefined;\n\n      _this.start(i, position[0]);\n\n      addHandlers(_this.getTouchEventMap());\n      stopPropagation(e);\n    };\n\n    _this.handleResize = () => {\n      // setTimeout of 0 gives element enough time to have assumed its new size if\n      // it is being resized\n      const resizeTimeout = window.setTimeout(() => {\n        // drop this timeout from pendingResizeTimeouts to reduce memory usage\n        _this.pendingResizeTimeouts.shift();\n\n        _this.resize();\n      }, 0);\n\n      _this.pendingResizeTimeouts.push(resizeTimeout);\n    };\n\n    _this.renderThumb = (style, i) => {\n      const className = _this.props.thumbClassName + \" \" + _this.props.thumbClassName + \"-\" + i + \" \" + (_this.state.index === i ? _this.props.thumbActiveClassName : '');\n      const props = {\n        'ref': r => {\n          _this[\"thumb\" + i] = r;\n        },\n        'key': _this.props.thumbClassName + \"-\" + i,\n        className,\n        style,\n        'onMouseDown': _this.createOnMouseDown(i),\n        'onTouchStart': _this.createOnTouchStart(i),\n        'onFocus': _this.createOnKeyDown(i),\n        'tabIndex': 0,\n        'role': 'slider',\n        'aria-orientation': _this.props.orientation,\n        'aria-valuenow': _this.state.value[i],\n        'aria-valuemin': _this.props.min,\n        'aria-valuemax': _this.props.max,\n        'aria-label': Array.isArray(_this.props.ariaLabel) ? _this.props.ariaLabel[i] : _this.props.ariaLabel,\n        'aria-labelledby': Array.isArray(_this.props.ariaLabelledby) ? _this.props.ariaLabelledby[i] : _this.props.ariaLabelledby,\n        'aria-disabled': _this.props.disabled\n      };\n      const state = {\n        index: i,\n        value: prepareOutValue(_this.state.value),\n        valueNow: _this.state.value[i]\n      };\n\n      if (_this.props.ariaValuetext) {\n        props['aria-valuetext'] = typeof _this.props.ariaValuetext === 'string' ? _this.props.ariaValuetext : _this.props.ariaValuetext(state);\n      }\n\n      return _this.props.renderThumb(props, state);\n    };\n\n    _this.renderTrack = (i, offsetFrom, offsetTo) => {\n      const props = {\n        key: _this.props.trackClassName + \"-\" + i,\n        className: _this.props.trackClassName + \" \" + _this.props.trackClassName + \"-\" + i,\n        style: _this.buildTrackStyle(offsetFrom, _this.state.upperBound - offsetTo)\n      };\n      const state = {\n        index: i,\n        value: prepareOutValue(_this.state.value)\n      };\n      return _this.props.renderTrack(props, state);\n    };\n\n    let value = sanitizeInValue(_props.value);\n\n    if (!value.length) {\n      value = sanitizeInValue(_props.defaultValue);\n    } // array for storing resize timeouts ids\n\n\n    _this.pendingResizeTimeouts = [];\n    const zIndices = [];\n\n    for (let i = 0; i < value.length; i += 1) {\n      value[i] = trimAlignValue(value[i], _props);\n      zIndices.push(i);\n    }\n\n    _this.resizeObserver = null;\n    _this.resizeElementRef = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createRef();\n    _this.state = {\n      index: -1,\n      upperBound: 0,\n      sliderLength: 0,\n      value,\n      zIndices\n    };\n    return _this;\n  }\n\n  var _proto = ReactSlider.prototype;\n\n  _proto.componentDidMount = function componentDidMount() {\n    if (typeof window !== 'undefined') {\n      this.resizeObserver = new ResizeObserver(this.handleResize);\n      this.resizeObserver.observe(this.resizeElementRef.current);\n      this.resize();\n    }\n  } // Keep the internal `value` consistent with an outside `value` if present.\n  // This basically allows the slider to be a controlled component.\n  ;\n\n  ReactSlider.getDerivedStateFromProps = function getDerivedStateFromProps(props, state) {\n    const value = sanitizeInValue(props.value);\n\n    if (!value.length) {\n      return null;\n    } // Do not allow controlled upates to happen while we have pending updates\n\n\n    if (state.pending) {\n      return null;\n    }\n\n    return {\n      value: value.map(item => trimAlignValue(item, props))\n    };\n  };\n\n  _proto.componentDidUpdate = function componentDidUpdate() {\n    // If an upperBound has not yet been determined (due to the component being hidden\n    // during the mount event, or during the last resize), then calculate it now\n    if (this.state.upperBound === 0) {\n      this.resize();\n    }\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.clearPendingResizeTimeouts();\n\n    if (this.resizeObserver) {\n      this.resizeObserver.disconnect();\n    }\n  };\n\n  _proto.onEnd = function onEnd(eventMap) {\n    if (eventMap) {\n      removeHandlers(eventMap);\n    }\n\n    if (this.hasMoved) {\n      this.fireChangeEvent('onAfterChange');\n    } // Allow controlled updates to continue\n\n\n    this.setState({\n      pending: false\n    });\n    this.hasMoved = false;\n  };\n\n  _proto.getValue = function getValue() {\n    return prepareOutValue(this.state.value);\n  };\n\n  _proto.getClosestIndex = function getClosestIndex(pixelOffset) {\n    let minDist = Number.MAX_VALUE;\n    let closestIndex = -1;\n    const {\n      value\n    } = this.state;\n    const l = value.length;\n\n    for (let i = 0; i < l; i += 1) {\n      const offset = this.calcOffset(value[i]);\n      const dist = Math.abs(pixelOffset - offset);\n\n      if (dist < minDist) {\n        minDist = dist;\n        closestIndex = i;\n      }\n    }\n\n    return closestIndex;\n  };\n\n  _proto.getMousePosition = function getMousePosition(e) {\n    return [e[\"page\" + this.axisKey()], e[\"page\" + this.orthogonalAxisKey()]];\n  };\n\n  _proto.getTouchPosition = function getTouchPosition(e) {\n    const touch = e.touches[0];\n    return [touch[\"page\" + this.axisKey()], touch[\"page\" + this.orthogonalAxisKey()]];\n  };\n\n  _proto.getKeyDownEventMap = function getKeyDownEventMap() {\n    return {\n      keydown: this.onKeyDown,\n      keyup: this.onKeyUp,\n      focusout: this.onBlur\n    };\n  };\n\n  _proto.getMouseEventMap = function getMouseEventMap() {\n    return {\n      mousemove: this.onMouseMove,\n      mouseup: this.onMouseUp\n    };\n  };\n\n  _proto.getTouchEventMap = function getTouchEventMap() {\n    return {\n      touchmove: this.onTouchMove,\n      touchend: this.onTouchEnd\n    };\n  };\n\n  _proto.getValueFromPosition = function getValueFromPosition(position) {\n    const diffValue = position / (this.state.sliderLength - this.state.thumbSize) * (this.props.max - this.props.min);\n    return trimAlignValue(this.state.startValue + diffValue, this.props);\n  };\n\n  _proto.getDiffPosition = function getDiffPosition(position) {\n    let diffPosition = position - this.state.startPosition;\n\n    if (this.props.invert) {\n      diffPosition *= -1;\n    }\n\n    return diffPosition;\n  } // create the `keydown` handler for the i-th thumb\n  ;\n\n  _proto.resize = function resize() {\n    const {\n      slider,\n      thumb0: thumb\n    } = this;\n\n    if (!slider || !thumb) {\n      return;\n    }\n\n    const sizeKey = this.sizeKey(); // For the slider size, we want to use the client width/height, excluding any borders\n\n    const sliderRect = slider.getBoundingClientRect();\n    const sliderSize = slider[sizeKey];\n    const sliderMax = sliderRect[this.posMaxKey()];\n    const sliderMin = sliderRect[this.posMinKey()]; // For the thumb size, we want to use the outer width/height, including any borders\n\n    const thumbRect = thumb.getBoundingClientRect();\n    const thumbSize = thumbRect[sizeKey.replace('client', '').toLowerCase()];\n    const upperBound = sliderSize - thumbSize;\n    const sliderLength = Math.abs(sliderMax - sliderMin);\n\n    if (this.state.upperBound !== upperBound || this.state.sliderLength !== sliderLength || this.state.thumbSize !== thumbSize) {\n      this.setState({\n        upperBound,\n        sliderLength,\n        thumbSize\n      });\n    }\n  } // calculates the offset of a thumb in pixels based on its value.\n  ;\n\n  _proto.calcOffset = function calcOffset(value) {\n    const range = this.props.max - this.props.min;\n\n    if (range === 0) {\n      return 0;\n    }\n\n    const ratio = (value - this.props.min) / range;\n    return ratio * this.state.upperBound;\n  } // calculates the value corresponding to a given pixel offset, i.e. the inverse of `calcOffset`.\n  ;\n\n  _proto.calcValue = function calcValue(offset) {\n    const ratio = offset / this.state.upperBound;\n    return ratio * (this.props.max - this.props.min) + this.props.min;\n  };\n\n  _proto.calcOffsetFromPosition = function calcOffsetFromPosition(position) {\n    const {\n      slider\n    } = this;\n    const sliderRect = slider.getBoundingClientRect();\n    const sliderMax = sliderRect[this.posMaxKey()];\n    const sliderMin = sliderRect[this.posMinKey()]; // The `position` value passed in is the mouse position based on the window height.\n    // The slider bounding rect is based on the viewport, so we must add the window scroll\n    // offset to normalize the values.\n\n    const windowOffset = window[\"page\" + this.axisKey() + \"Offset\"];\n    const sliderStart = windowOffset + (this.props.invert ? sliderMax : sliderMin);\n    let pixelOffset = position - sliderStart;\n\n    if (this.props.invert) {\n      pixelOffset = this.state.sliderLength - pixelOffset;\n    }\n\n    pixelOffset -= this.state.thumbSize / 2;\n    return pixelOffset;\n  } // Snaps the nearest thumb to the value corresponding to `position`\n  // and calls `callback` with that thumb's index.\n  ;\n\n  _proto.forceValueFromPosition = function forceValueFromPosition(position, callback) {\n    const pixelOffset = this.calcOffsetFromPosition(position);\n    const closestIndex = this.getClosestIndex(pixelOffset);\n    const nextValue = trimAlignValue(this.calcValue(pixelOffset), this.props); // Clone this.state.value since we'll modify it temporarily\n    // eslint-disable-next-line zillow/react/no-access-state-in-setstate\n\n    const value = this.state.value.slice();\n    value[closestIndex] = nextValue; // Prevents the slider from shrinking below `props.minDistance`\n\n    for (let i = 0; i < value.length - 1; i += 1) {\n      if (value[i + 1] - value[i] < this.props.minDistance) {\n        return;\n      }\n    }\n\n    this.fireChangeEvent('onBeforeChange');\n    this.hasMoved = true;\n    this.setState({\n      value\n    }, () => {\n      callback(closestIndex);\n      this.fireChangeEvent('onChange');\n    });\n  } // clear all pending timeouts to avoid error messages after unmounting\n  ;\n\n  _proto.clearPendingResizeTimeouts = function clearPendingResizeTimeouts() {\n    do {\n      const nextTimeout = this.pendingResizeTimeouts.shift();\n      clearTimeout(nextTimeout);\n    } while (this.pendingResizeTimeouts.length);\n  };\n\n  _proto.start = function start(i, position) {\n    const thumbRef = this[\"thumb\" + i];\n\n    if (thumbRef) {\n      thumbRef.focus();\n    }\n\n    const {\n      zIndices\n    } = this.state; // remove wherever the element is\n\n    zIndices.splice(zIndices.indexOf(i), 1); // add to end\n\n    zIndices.push(i);\n    this.setState(prevState => ({\n      startValue: prevState.value[i],\n      startPosition: position !== undefined ? position : prevState.startPosition,\n      index: i,\n      zIndices\n    }));\n  };\n\n  _proto.moveUpByStep = function moveUpByStep(step) {\n    if (step === void 0) {\n      step = this.props.step;\n    }\n\n    const oldValue = this.state.value[this.state.index]; // if the slider is inverted and horizontal we want to honor the inverted value\n\n    const newValue = this.props.invert && this.props.orientation === 'horizontal' ? oldValue - step : oldValue + step;\n    const trimAlign = trimAlignValue(newValue, this.props);\n    this.move(Math.min(trimAlign, this.props.max));\n  };\n\n  _proto.moveDownByStep = function moveDownByStep(step) {\n    if (step === void 0) {\n      step = this.props.step;\n    }\n\n    const oldValue = this.state.value[this.state.index]; // if the slider is inverted and horizontal we want to honor the inverted value\n\n    const newValue = this.props.invert && this.props.orientation === 'horizontal' ? oldValue + step : oldValue - step;\n    const trimAlign = trimAlignValue(newValue, this.props);\n    this.move(Math.max(trimAlign, this.props.min));\n  };\n\n  _proto.move = function move(newValue) {\n    // Clone this.state.value since we'll modify it temporarily\n    // eslint-disable-next-line zillow/react/no-access-state-in-setstate\n    const value = this.state.value.slice();\n    const {\n      index\n    } = this.state;\n    const {\n      length\n    } = value; // Short circuit if the value is not changing\n\n    const oldValue = value[index];\n\n    if (newValue === oldValue) {\n      return;\n    } // Trigger only before the first movement\n\n\n    if (!this.hasMoved) {\n      this.fireChangeEvent('onBeforeChange');\n    }\n\n    this.hasMoved = true; // if \"pearling\" (= thumbs pushing each other) is disabled,\n    // prevent the thumb from getting closer than `minDistance` to the previous or next thumb.\n\n    const {\n      pearling,\n      max,\n      min,\n      minDistance\n    } = this.props;\n\n    if (!pearling) {\n      if (index > 0) {\n        const valueBefore = value[index - 1];\n\n        if (newValue < valueBefore + minDistance) {\n          // eslint-disable-next-line no-param-reassign\n          newValue = valueBefore + minDistance;\n        }\n      }\n\n      if (index < length - 1) {\n        const valueAfter = value[index + 1];\n\n        if (newValue > valueAfter - minDistance) {\n          // eslint-disable-next-line no-param-reassign\n          newValue = valueAfter - minDistance;\n        }\n      }\n    }\n\n    value[index] = newValue; // if \"pearling\" is enabled, let the current thumb push the pre- and succeeding thumbs.\n\n    if (pearling && length > 1) {\n      if (newValue > oldValue) {\n        this.pushSucceeding(value, minDistance, index);\n        trimSucceeding(length, value, minDistance, max);\n      } else if (newValue < oldValue) {\n        this.pushPreceding(value, minDistance, index);\n        trimPreceding(length, value, minDistance, min);\n      }\n    } // Normally you would use `shouldComponentUpdate`,\n    // but since the slider is a low-level component,\n    // the extra complexity might be worth the extra performance.\n\n\n    this.setState({\n      value\n    }, this.fireChangeEvent.bind(this, 'onChange'));\n  };\n\n  _proto.pushSucceeding = function pushSucceeding(value, minDistance, index) {\n    let i;\n    let padding;\n\n    for (i = index, padding = value[i] + minDistance; value[i + 1] !== null && padding > value[i + 1]; i += 1, padding = value[i] + minDistance) {\n      // eslint-disable-next-line no-param-reassign\n      value[i + 1] = alignValue(padding, this.props);\n    }\n  };\n\n  _proto.pushPreceding = function pushPreceding(value, minDistance, index) {\n    for (let i = index, padding = value[i] - minDistance; value[i - 1] !== null && padding < value[i - 1]; i -= 1, padding = value[i] - minDistance) {\n      // eslint-disable-next-line no-param-reassign\n      value[i - 1] = alignValue(padding, this.props);\n    }\n  };\n\n  _proto.axisKey = function axisKey() {\n    if (this.props.orientation === 'vertical') {\n      return 'Y';\n    } // Defaults to 'horizontal';\n\n\n    return 'X';\n  };\n\n  _proto.orthogonalAxisKey = function orthogonalAxisKey() {\n    if (this.props.orientation === 'vertical') {\n      return 'X';\n    } // Defaults to 'horizontal'\n\n\n    return 'Y';\n  };\n\n  _proto.posMinKey = function posMinKey() {\n    if (this.props.orientation === 'vertical') {\n      return this.props.invert ? 'bottom' : 'top';\n    } // Defaults to 'horizontal'\n\n\n    return this.props.invert ? 'right' : 'left';\n  };\n\n  _proto.posMaxKey = function posMaxKey() {\n    if (this.props.orientation === 'vertical') {\n      return this.props.invert ? 'top' : 'bottom';\n    } // Defaults to 'horizontal'\n\n\n    return this.props.invert ? 'left' : 'right';\n  };\n\n  _proto.sizeKey = function sizeKey() {\n    if (this.props.orientation === 'vertical') {\n      return 'clientHeight';\n    } // Defaults to 'horizontal'\n\n\n    return 'clientWidth';\n  };\n\n  _proto.fireChangeEvent = function fireChangeEvent(event) {\n    if (this.props[event]) {\n      this.props[event](prepareOutValue(this.state.value), this.state.index);\n    }\n  };\n\n  _proto.buildThumbStyle = function buildThumbStyle(offset, i) {\n    const style = {\n      position: 'absolute',\n      touchAction: 'none',\n      willChange: this.state.index >= 0 ? this.posMinKey() : undefined,\n      zIndex: this.state.zIndices.indexOf(i) + 1\n    };\n    style[this.posMinKey()] = offset + \"px\";\n    return style;\n  };\n\n  _proto.buildTrackStyle = function buildTrackStyle(min, max) {\n    const obj = {\n      position: 'absolute',\n      willChange: this.state.index >= 0 ? this.posMinKey() + \",\" + this.posMaxKey() : undefined\n    };\n    obj[this.posMinKey()] = min;\n    obj[this.posMaxKey()] = max;\n    return obj;\n  };\n\n  _proto.buildMarkStyle = function buildMarkStyle(offset) {\n    var _ref;\n\n    return _ref = {\n      position: 'absolute'\n    }, _ref[this.posMinKey()] = offset, _ref;\n  };\n\n  _proto.renderThumbs = function renderThumbs(offset) {\n    const {\n      length\n    } = offset;\n    const styles = [];\n\n    for (let i = 0; i < length; i += 1) {\n      styles[i] = this.buildThumbStyle(offset[i], i);\n    }\n\n    const res = [];\n\n    for (let i = 0; i < length; i += 1) {\n      res[i] = this.renderThumb(styles[i], i);\n    }\n\n    return res;\n  };\n\n  _proto.renderTracks = function renderTracks(offset) {\n    const tracks = [];\n    const lastIndex = offset.length - 1;\n    tracks.push(this.renderTrack(0, 0, offset[0]));\n\n    for (let i = 0; i < lastIndex; i += 1) {\n      tracks.push(this.renderTrack(i + 1, offset[i], offset[i + 1]));\n    }\n\n    tracks.push(this.renderTrack(lastIndex + 1, offset[lastIndex], this.state.upperBound));\n    return tracks;\n  };\n\n  _proto.renderMarks = function renderMarks() {\n    let {\n      marks\n    } = this.props;\n    const range = this.props.max - this.props.min + 1;\n\n    if (typeof marks === 'boolean') {\n      marks = Array.from({\n        length: range\n      }).map((_, key) => key);\n    } else if (typeof marks === 'number') {\n      marks = Array.from({\n        length: range\n      }).map((_, key) => key).filter(key => key % marks === 0);\n    }\n\n    return marks.map(parseFloat).sort((a, b) => a - b).map(mark => {\n      const offset = this.calcOffset(mark);\n      const props = {\n        key: mark,\n        className: this.props.markClassName,\n        style: this.buildMarkStyle(offset)\n      };\n      return this.props.renderMark(props);\n    });\n  };\n\n  _proto.render = function render() {\n    const offset = [];\n    const {\n      value\n    } = this.state;\n    const l = value.length;\n\n    for (let i = 0; i < l; i += 1) {\n      offset[i] = this.calcOffset(value[i], i);\n    }\n\n    const tracks = this.props.withTracks ? this.renderTracks(offset) : null;\n    const thumbs = this.renderThumbs(offset);\n    const marks = this.props.marks ? this.renderMarks() : null;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement('div', {\n      ref: r => {\n        this.slider = r;\n        this.resizeElementRef.current = r;\n      },\n      style: {\n        position: 'relative'\n      },\n      className: this.props.className + (this.props.disabled ? ' disabled' : ''),\n      onMouseDown: this.onSliderMouseDown,\n      onClick: this.onSliderClick\n    }, tracks, thumbs, marks);\n  };\n\n  return ReactSlider;\n}(react__WEBPACK_IMPORTED_MODULE_2__.Component);\n\nReactSlider.displayName = 'ReactSlider';\nReactSlider.defaultProps = {\n  min: 0,\n  max: 100,\n  step: 1,\n  pageFn: step => step * 10,\n  minDistance: 0,\n  defaultValue: 0,\n  orientation: 'horizontal',\n  className: 'slider',\n  thumbClassName: 'thumb',\n  thumbActiveClassName: 'active',\n  trackClassName: 'track',\n  markClassName: 'mark',\n  withTracks: true,\n  pearling: false,\n  disabled: false,\n  snapDragDisabled: false,\n  invert: false,\n  marks: [],\n  renderThumb: props => /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"div\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__({}, props, {\n    __self: ReactSlider,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 31\n    }\n  })),\n  renderTrack: props => /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"div\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__({}, props, {\n    __self: ReactSlider,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 31\n    }\n  })),\n  renderMark: props => /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"span\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__({}, props, {\n    __self: ReactSlider,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 30\n    }\n  }))\n};\nReactSlider.propTypes =  true ? {\n  /**\n   * The minimum value of the slider.\n   */\n  min: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n\n  /**\n   * The maximum value of the slider.\n   */\n  max: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n\n  /**\n   * Value to be added or subtracted on each step the slider makes.\n   * Must be greater than zero.\n   * `max - min` should be evenly divisible by the step value.\n   */\n  step: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n\n  /**\n   * The result of the function is the value to be added or subtracted\n   * when the `Page Up` or `Page Down` keys are pressed.\n   *\n   * The current `step` value will be passed as the only argument.\n   * By default, paging will modify `step` by a factor of 10.\n   */\n  pageFn: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * The minimal distance between any pair of thumbs.\n   * Must be positive, but zero means they can sit on top of each other.\n   */\n  minDistance: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n\n  /**\n   * Determines the initial positions of the thumbs and the number of thumbs.\n   *\n   * If a number is passed a slider with one thumb will be rendered.\n   * If an array is passed each value will determine the position of one thumb.\n   * The values in the array must be sorted.\n   */\n  defaultValue: prop_types__WEBPACK_IMPORTED_MODULE_3__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_3__.number, prop_types__WEBPACK_IMPORTED_MODULE_3__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_3__.number)]),\n\n  /**\n   * Like `defaultValue` but for\n   * [controlled components](http://facebook.github.io/react/docs/forms.html#controlled-components).\n   */\n  // eslint-disable-next-line zillow/react/require-default-props\n  value: prop_types__WEBPACK_IMPORTED_MODULE_3__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_3__.number, prop_types__WEBPACK_IMPORTED_MODULE_3__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_3__.number)]),\n\n  /**\n   * Determines whether the slider moves horizontally (from left to right)\n   * or vertically (from top to bottom).\n   */\n  orientation: prop_types__WEBPACK_IMPORTED_MODULE_3__.oneOf(['horizontal', 'vertical']),\n\n  /**\n   * The css class set on the slider node.\n   */\n  className: prop_types__WEBPACK_IMPORTED_MODULE_3__.string,\n\n  /**\n   * The css class set on each thumb node.\n   *\n   * In addition each thumb will receive a numbered css class of the form\n   * `${thumbClassName}-${i}`, e.g. `thumb-0`, `thumb-1`, ...\n   */\n  thumbClassName: prop_types__WEBPACK_IMPORTED_MODULE_3__.string,\n\n  /**\n   * The css class set on the thumb that is currently being moved.\n   */\n  thumbActiveClassName: prop_types__WEBPACK_IMPORTED_MODULE_3__.string,\n\n  /**\n   * If `true` tracks between the thumbs will be rendered.\n   */\n  withTracks: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * The css class set on the tracks between the thumbs.\n   * In addition track fragment will receive a numbered css class of the form\n   * `${trackClassName}-${i}`, e.g. `track-0`, `track-1`, ...\n   */\n  trackClassName: prop_types__WEBPACK_IMPORTED_MODULE_3__.string,\n\n  /**\n   * If `true` the active thumb will push other thumbs\n   * within the constraints of `min`, `max`, `step` and `minDistance`.\n   */\n  pearling: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * If `true` the thumbs can't be moved.\n   */\n  disabled: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * Disables thumb move when clicking the slider track\n   */\n  snapDragDisabled: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * Inverts the slider.\n   */\n  invert: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * Shows passed marks on the track, if true it shows all the marks,\n   * if an array of numbers it shows just the passed marks, if a number is passed\n   * it shows just the marks in that steps: like passing 3 shows the marks 3, 6, 9\n   */\n  marks: prop_types__WEBPACK_IMPORTED_MODULE_3__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_3__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_3__.number), prop_types__WEBPACK_IMPORTED_MODULE_3__.bool, prop_types__WEBPACK_IMPORTED_MODULE_3__.number]),\n\n  /**\n   * The css class set on the marks.\n   */\n  markClassName: prop_types__WEBPACK_IMPORTED_MODULE_3__.string,\n\n  /**\n   * Callback called before starting to move a thumb. The callback will only be called if the\n   * action will result in a change. The function will be called with two arguments, the first\n   * being the initial value(s) the second being thumb index.\n   */\n  // eslint-disable-next-line max-len\n  // eslint-disable-next-line zillow/react/require-default-props, zillow/react/no-unused-prop-types\n  onBeforeChange: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Callback called on every value change.\n   * The function will be called with two arguments, the first being the new value(s)\n   * the second being thumb index.\n   */\n  // eslint-disable-next-line max-len\n  // eslint-disable-next-line zillow/react/require-default-props, zillow/react/no-unused-prop-types\n  onChange: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Callback called only after moving a thumb has ended. The callback will only be called if\n   * the action resulted in a change. The function will be called with two arguments, the\n   * first being the result value(s) the second being thumb index.\n   */\n  // eslint-disable-next-line max-len\n  // eslint-disable-next-line zillow/react/require-default-props, zillow/react/no-unused-prop-types\n  onAfterChange: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Callback called when the the slider is clicked (thumb or tracks).\n   * Receives the value at the clicked position as argument.\n   */\n  // eslint-disable-next-line zillow/react/require-default-props\n  onSliderClick: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * aria-label for screen-readers to apply to the thumbs.\n   * Use an array for more than one thumb.\n   * The length of the array must match the number of thumbs in the value array.\n   */\n  // eslint-disable-next-line zillow/react/require-default-props\n  ariaLabel: prop_types__WEBPACK_IMPORTED_MODULE_3__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_3__.string, prop_types__WEBPACK_IMPORTED_MODULE_3__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_3__.string)]),\n\n  /**\n   * aria-labelledby for screen-readers to apply to the thumbs.\n   * Used when slider rendered with separate label.\n   * Use an array for more than one thumb.\n   * The length of the array must match the number of thumbs in the value array.\n   */\n  // eslint-disable-next-line zillow/react/require-default-props\n  ariaLabelledby: prop_types__WEBPACK_IMPORTED_MODULE_3__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_3__.string, prop_types__WEBPACK_IMPORTED_MODULE_3__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_3__.string)]),\n\n  /**\n   * aria-valuetext for screen-readers.\n   * Can be a static string, or a function that returns a string.\n   * The function will be passed a single argument,\n   * an object with the following properties:\n   *\n   *     state => `Value: ${state.value}`\n   *\n   * - `state.index` {`number`} the index of the thumb\n   * - `state.value` {`number` | `array`} the current value state\n   * - `state.valueNow` {`number`} the value of the thumb (i.e. aria-valuenow)\n   */\n  // eslint-disable-next-line zillow/react/require-default-props\n  ariaValuetext: prop_types__WEBPACK_IMPORTED_MODULE_3__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_3__.string, prop_types__WEBPACK_IMPORTED_MODULE_3__.func]),\n\n  /**\n   * Provide a custom render function for the track node.\n   * The render function will be passed two arguments,\n   * an object with props that should be added to your handle node,\n   * and an object with track and slider state:\n   *\n   *     (props, state) => <div {...props} />\n   *\n   * - `props` {`object`} props to be spread into your track node\n   * - `state.index` {`number`} the index of the track\n   * - `state.value` {`number` | `array`} the current value state\n   */\n  renderTrack: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Provide a custom render function for dynamic thumb content.\n   * The render function will be passed two arguments,\n   * an object with props that should be added to your thumb node,\n   * and an object with thumb and slider state:\n   *\n   *     (props, state) => <div {...props} />\n   *\n   * - `props` {`object`} props to be spread into your thumb node\n   * - `state.index` {`number`} the index of the thumb\n   * - `state.value` {`number` | `array`} the current value state\n   * - `state.valueNow` {`number`} the value of the thumb (i.e. aria-valuenow)\n   */\n  // eslint-disable-next-line zillow/react/require-default-props\n  renderThumb: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Provide a custom render function for the mark node.\n   * The render function will be passed one argument,\n   * an object with props that should be added to your handle node\n   *\n   *     (props) => <span {...props} />\n   *\n   * - `props` {`object`} props to be spread into your track node\n   */\n  renderMark: prop_types__WEBPACK_IMPORTED_MODULE_3__.func\n} : 0;\nvar ReactSlider$1 = ReactSlider;\n\n\n//# sourceMappingURL=ReactSlider.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-slider/dist/es/dev/components/ReactSlider/ReactSlider.mjs\n");

/***/ })

};
;