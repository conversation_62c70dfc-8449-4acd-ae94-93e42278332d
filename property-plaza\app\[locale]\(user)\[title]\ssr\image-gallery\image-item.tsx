"use client"
import { useFavoriteListing } from "@/hooks/use-post-favorite-listing";
import { imagePlaceholder } from "@/lib/constanta/image-placeholder";
import Image from "next/image";
import useImageGallery from "../utils/use-image-gallery";
import { packages } from "@/core/domain/subscription/subscription";

export default function ImageItem({ imageUrl, alt }: { imageUrl: string, alt?: string }) {
  const { authenticated: isAuthenticated, membership } = useFavoriteListing("")
  const { handleOpenAuthDialog, handleOpenSubscriptionDialog, handleOpenImageDetailDialog } = useImageGallery()

  const handleClick = () => {
    if (!isAuthenticated) return handleOpenAuthDialog()
    // if (membership == packages.free) return handleOpenSubscriptionDialog()
    return handleOpenImageDetailDialog()
  }
  return <>
    <div className="absolute inset-0 z-10 pointer-events-none watermark-overlay" />
    <Image
      src={imageUrl || ""}
      alt={alt || ""}
      fill
      sizes="100vw"
      priority
      blurDataURL={imagePlaceholder}
      placeholder="blur"
      onClick={handleClick}
      style={{
        objectFit: "cover"
      }}
    />
  </>

}