import { transformMeta } from "../utils/transform";
import {
  getAllListings,
  getBatchProperties,
  getDetailListing,
  getDetailListingSeekers,
  getLocationSuggestion,
  getSeekersFavoriteListing,
  getSeekersFilterParameter,
  getSeekersListing,
  postFilteredSeekeresListings,
  saveSearchListing,
  ssrGetAllProperties,
  ssrGetSaveSearchHistory,
  ssrGetSeekersListing,
} from "./api";
import {
  GetFilteredSeekersListingDto,
  GetHomePageSeekersDto,
  GetLocationSuggestionDto,
  GetSeekersListingDto,
  ListingSeekersDto,
} from "./dto";
import {
  transformDetailListing,
  transformLocationSuggestionListing,
  transformPropertiesSitemap,
  transformSeekersListing,
  transfromListings,
  transfromSeekersFilterParameter,
} from "./transform";
import { BasePaginationRequest } from "@/core/utils/types";

export async function getAllListingsService(
  searchParam: BasePaginationRequest
) {
  try {
    const request = await getAllListings(searchParam);
    return {
      data: transfromListings(request.data.data.items),
      meta: transformMeta(request.data.data.meta),
    };
  } catch (e: any) {
    return {
      error: e.data.error ?? "An unknown error occurred",
    };
  }
}

export async function getDetailListingService(id: string) {
  try {
    const request = await getDetailListing(id);
    return {
      data: transformDetailListing(request.data.data),
      meta: undefined,
    };
  } catch (e: any) {
    return {
      error: e.data.error ?? "An unknown error occurred",
    };
  }
}

export async function getBatchListingByIdService(codes: string[]) {
  try {
    const request = await getBatchProperties({
      property_list: codes,
    });
    return {
      data: transformSeekersListing(request.data.data),
    };
  } catch (err: any) {
    return {
      error: err.data.error ?? "An unknown error occurred",
    };
  }
}

// this function is dedicated on SSR request
export async function getHomepageSeekersListingService() {
  try {
    const request = await ssrGetSeekersListing<GetHomePageSeekersDto>(
      { limit: 8, section: "ALL" },
      ""
    );
    if (request.error) {
      throw Error(request.error.name, {
        cause: request.error.message,
      });
    }
    return {
      data: {
        newest: transformSeekersListing(request.data?.newest || []),
        popular: transformSeekersListing(request.data?.popular || []),
        featured: transformSeekersListing(request.data?.featured || []),
        commercial: transformSeekersListing(request.data?.commercial || []),
      },
    };
  } catch (e: any) {
    return {
      error: e.error ?? "An unknown error occurred",
    };
  }
}

export async function getSearchHistoryService() {
  try {
    const request = await ssrGetSaveSearchHistory();
  } catch (err: unknown) {
    return;
  }
}

export async function getSeekersListingService(
  data: GetSeekersListingDto,
  isSSR?: boolean,
  tag?: string
) {
  try {
    let response;
    if (isSSR) {
      const request = await ssrGetSeekersListing<ListingSeekersDto>(data, tag!);
      response = request.data;

      if (request.error) {
        throw Error(request.error.name, {
          cause: request.error.message,
        });
      }
    } else {
      const request = await getSeekersListing(data);
      response = request.data.data;
    }
    return {
      data: transformSeekersListing(response),
    };
  } catch (e: any) {
    return {
      error: e.error ?? "An unknown error occurred",
    };
  }
}

export async function getFilteredSeekersListingService(
  data: GetFilteredSeekersListingDto
) {
  try {
    const request = await postFilteredSeekeresListings(data);
    try {
      const cleanedFilter = Object.fromEntries(
        Object.entries(data).filter(([_, v]) => v !== undefined)
      );
      if (Object.keys(cleanedFilter).length !== 2) {
        await saveSearchListing(data);
      }
    } catch (err: unknown) {}

    return {
      data: transformSeekersListing(request.data.data.items),
      meta: transformMeta(request.data.data.meta),
    };
  } catch (e: any) {
    return {
      error: e.data.error ?? "An unknown error occurred",
    };
  }
}

export async function getLocationSuggestionService(
  data: GetLocationSuggestionDto
) {
  // minimum character for search is 3
  if (data.search.length < 3) return { data: [] };
  try {
    const request = await getLocationSuggestion(data);
    return {
      data: transformLocationSuggestionListing(data.search, request.data.data),
    };
  } catch (e: any) {
    return {
      error: e.data.error ?? "An unknown error occurred",
    };
  }
}

export async function getDetailListingSeekersService(id: string) {
  try {
    const request = await getDetailListingSeekers(id);
    return {
      data: transformDetailListing(request.data.data),
      meta: undefined,
    };
  } catch (e: any) {
    return {
      error: e.data.error ?? "An unknown error occurred",
    };
  }
}

export async function getFilterParameterSeekersService() {
  try {
    const request = await getSeekersFilterParameter();
    return {
      data: transfromSeekersFilterParameter(request.data.data),
      meta: undefined,
    };
  } catch (e: any) {
    return {
      error: e.data.error ?? "An unknown error occurred",
    };
  }
}

export async function getFavoriteSeekersListingService(
  data: GetFilteredSeekersListingDto
) {
  try {
    const request = await getSeekersFavoriteListing({
      page: +data.page,
      per_page: +data.per_page,
      search: data.search || "",
      sort_by: data.sort_by,
    });
    return {
      data: transformSeekersListing(request.data.data.items),
      meta: transformMeta(request.data.data.meta),
    };
  } catch (e: any) {
    return {
      error: e.data.error ?? "An unknown error occurred",
    };
  }
}

export async function getSSRSitemapPropertiesService() {
  try {
    let response;
    const request = await ssrGetAllProperties<{
      meta: unknown;
      items: ListingSeekersDto[];
    }>();
    response = request?.data?.items || [];
    if (request.error) {
      throw Error(request.error.name, {
        cause: request.error.message,
      });
    }

    return {
      data: transformPropertiesSitemap(response),
    };
  } catch (e: any) {
    return {
      error: e.error ?? "An unknown error occurred",
    };
  }
}
