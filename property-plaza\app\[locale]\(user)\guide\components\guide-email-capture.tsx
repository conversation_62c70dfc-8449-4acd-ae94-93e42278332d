"use client"

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { Mail, Download, CheckCircle } from "lucide-react";

const formSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  email: z.string().email("Please enter a valid email address"),
  consent: z.boolean().refine((val) => val === true, {
    message: "Please agree to receive updates",
  }),
});

type FormData = z.infer<typeof formSchema>;

export default function GuideEmailCapture() {
  const t = useTranslations("guide");
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      email: "",
      consent: false,
    },
  });

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    
    try {
      // Submit to waiting list API
      const response = await fetch('/api/v1/waiting-list', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: data.firstName,
          email: data.email,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit');
      }

      // Track form submission
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'lead_magnet_form_submit', {
          event_category: 'Lead Magnet',
          event_label: 'Email Capture',
          value: 1
        });
      }

      setIsSuccess(true);
      
      toast({
        title: "Success! Check your email",
        description: "We've sent you the Bali Housing Guide. Check your inbox (and spam folder) in the next few minutes.",
      });

    } catch (error) {
      console.error('Form submission error:', error);
      toast({
        title: "Oops! Something went wrong",
        description: "Please try again or contact us if the problem persists.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSuccess) {
    return (
      <section id="email-capture-section" className="py-20 bg-gradient-to-br from-seekers-primary-lightest to-neutral-lightest">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white rounded-3xl p-12 shadow-xl">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="h-10 w-10 text-green-600" />
            </div>
            
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Check Your Email!
            </h2>
            
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              We've sent the <strong>Bali Housing Guide</strong> to your inbox. 
              If you don't see it in a few minutes, check your spam folder.
            </p>
            
            <div className="bg-gray-50 rounded-2xl p-6 mb-8">
              <h3 className="font-semibold text-gray-900 mb-2">What's Next?</h3>
              <p className="text-gray-600 text-sm">
                While you're reading the guide, browse our verified property listings 
                to see what's available in your preferred areas.
              </p>
            </div>
            
            <Button
              onClick={() => window.open('/', '_blank')}
              size="lg"
              className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-4 text-lg font-semibold rounded-lg"
            >
              Browse Properties Now
            </Button>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="email-capture-section" className="py-20 bg-gradient-to-br from-seekers-primary-lightest to-neutral-lightest">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-3xl p-8 md:p-12 shadow-xl">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-seekers-primary-lighter rounded-full flex items-center justify-center mx-auto mb-6">
              <Mail className="h-8 w-8 text-seekers-primary" />
            </div>
            
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Get Your Guide Instantly
            </h2>
            
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Enter your email below and we'll send you the complete Bali Housing Guide 
              within the next 2 minutes.
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        First Name *
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Your first name"
                          className="h-12 text-lg border-gray-300 focus:border-seekers-primary focus:ring-seekers-primary"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Email Address *
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          className="h-12 text-lg border-gray-300 focus:border-seekers-primary focus:ring-seekers-primary"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="consent"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="mt-1"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-sm text-gray-600">
                        I agree to receive helpful updates about Bali housing and Property Plaza. 
                        No spam, unsubscribe anytime.
                      </FormLabel>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                disabled={isSubmitting}
                size="lg"
                className="w-full bg-teal-600 hover:bg-teal-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Sending Guide...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-5 w-5" />
                    Send Me the Guide
                  </>
                )}
              </Button>

              <p className="text-center text-sm text-gray-500">
                No spam. Unsubscribe anytime. We respect your privacy.
              </p>
            </form>
          </Form>
        </div>
      </div>
    </section>
  );
}
