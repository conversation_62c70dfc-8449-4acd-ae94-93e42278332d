// import SeekersFooter from "@/components/footer/seeker-footer";
import SeekersBanner from "@/components/navbar/seekers-banner";
import SeekersNavbar from "@/components/navbar/seekers-navbar-2";
import React from "react";
import SetupSeekersStore from "./setup-seekers";
import { cookies } from "next/headers";
import PopUp from "./pop-up";
import NotificationProvider from "@/components/providers/notification-provider";
import dynamic from "next/dynamic";

const SeekersFooter = dynamic(() => import("@/components/footer/seeker-footer"), { ssr: false })
export default async function Userlayout({ children }: { children: React.ReactNode }) {
  const cookiesStore = cookies()
  const settingStore = cookiesStore.get("seekers-settings")?.value || ""
  const settings = settingStore ? JSON.parse(settingStore) : undefined
  const locale = cookiesStore.get('NEXT_LOCALE')?.value
  // const listingHistory = 
  return <>
    <NotificationProvider isSeeker />
    <SetupSeekersStore />
    <SeekersBanner />
    <div className="w-full sticky top-0 z-10 bg-white !mt-0" >
      <SeekersNavbar currency_={settings?.state?.currency} localeId={locale} />
    </div>
    <div className="!mt-0 relative min-h-screen max-sm:max-w-screen-sm">
      {children}
    </div>
    <div className="!mt-0">
      <SeekersFooter />
    </div>
    <PopUp />
  </ >
}