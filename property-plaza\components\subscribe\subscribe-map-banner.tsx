
import { cn } from "@/lib/utils";
import { Alert, AlertDescription } from "../ui/alert";
import { useSeekersSearchMapUtil } from "@/stores/seekers-search-map-utils";
import Link from "next/link";
import { Button } from "../ui/button";
import { useTranslations } from "next-intl";
import { plansUrl, noLoginPlanUrl } from "@/lib/constanta/route";
import { useUserStore } from "@/stores/user.store";

// Use this only on maps
export default function SubscribeMapBanner({ isSubscribe, className }: { isSubscribe?: Boolean, className?: string }) {
  const viewMode = useSeekersSearchMapUtil(state => state.viewMode)
  const { email } = useUserStore(state => state.seekers)

  const t = useTranslations("seeker")
  return <>
    {
      !isSubscribe ?
        <Alert className={cn("max-w-xs bg-[#B48B5599] font-semibold text-white  shadow-md absolute   z-10",
          viewMode == "map" ? "top-4 right-4 max-sm:right-1/2 max-sm:translate-x-1/2" : "top-16 right-1/2 translate-x-1/2", className)}>
          {/*
          <AlertTitle>Subscribe for more feature</AlertTitle> */}
          <AlertDescription className="text-xs">
            {t('misc.subscibePropgram.searchPage.description')} {" "}
            <Button asChild variant={"link"} size={"sm"} className="p-0 h-fit w-fit text-white underline">
              <Link href={email ? plansUrl : noLoginPlanUrl}>{t('cta.subscribe')}</Link>
            </Button>
          </AlertDescription >
        </Alert >
        : <></>
    }
  </>
}