import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Label } from "@/components/ui/label"
import useSeekersSearch from "@/hooks/use-seekers-search"
import { cn } from "@/lib/utils"
import { useSeekerSearchStore } from "@/stores/seeker-search.store"
import { useTranslations } from "next-intl"
import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import PropertyFormatter from "@/app/[locale]/(user)/s/property-type-formatter"
import { Check, X } from "lucide-react"
import ListingCategoryIcon from "@/app/[locale]/(user)/s/listing-category-icon"

export default function TypeSearch({ customTrigger }: { customTrigger?: React.ReactNode }) {
  const t = useTranslations()
  const [openInput, setOpen] = useState(false)
  const { isOpen, setCategoryInputFocused } = useSeekerSearchStore(state => state)
  const { handleSetType, seekersSearch, propertyType } = useSeekersSearch()
  const handleOpen = (open: boolean) => {
    setOpen(open)
    setCategoryInputFocused(open)
  }
  return <DropdownMenu modal={false} open={openInput} onOpenChange={handleOpen} >
    <DropdownMenuTrigger asChild>
      {customTrigger ||
        <div
          className={cn("px-2", isOpen ? "w-full" : "w-0")}>
          <Label className="text-xs font-medium text-seekers-text">{t('seeker.navbar.search.category')}</Label>
          <motion.div
            animate={{
              height: !isOpen ? 0 : 20,
              opacity: !isOpen ? 0 : 100,
              width: !isOpen ? 0 : "100%"
            }}
            transition={{ duration: 0.3 }}
            className="flex justify-between"
          >
            <Button variant={"ghost"} className="w-full h-fit font-normal p-0 overflow-hidden justify-start hover:bg-transparent">
              {
                seekersSearch.propertyType.length < 1 ?
                  <p className="text-seekers-text-lighter">{t('seeker.navbar.search.propertyType')}</p>
                  :

                  <PropertyFormatter value={seekersSearch.propertyType.toString()} />
              }
            </Button>
            <Button
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                seekersSearch.clearCategory()
              }}
              size={"icon"}
              className={cn("-mt-2", seekersSearch.propertyType.length > 0 ? "" : "hidden")}>
              <X />
            </Button>
          </motion.div>
        </div>
      }
    </DropdownMenuTrigger>
    <DropdownMenuContent
      className={cn("border-seekers-text-lighter/20 grid grid-cols-2 sm:grid-cols-3 gap-3 p-4",
        !isOpen ? "w-0" : "w-fit")}
      align="start"
    >
      {propertyType.map(item =>
        <div
          className="
                 border-seekers-text-lighter
                    hover:bg-seekers-background 
                    gap-2 text-xs 
                    text-seekers-text-light 
                    flex flex-col 
                    justify-center
                    items-center
                    md:w-28 p-4
                    relative
                    border
                    rounded-lg
          "
          key={item.id}
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            handleSetType(item.value)
          }}
          data-inside-dropdown

        >
          <div className={cn(
            "absolute top-3 left-3 rounded-full w-4 h-4 flex items-center justify-center",
            seekersSearch.propertyType.includes(item.value) ? "bg-seekers-primary" : "border border-seekers-text-lighter"
          )}>
            <Check className={cn(seekersSearch.propertyType.includes(item.value) ? "w-3 h-3 text-white" : "hidden")} />

          </div>

          <ListingCategoryIcon category={item.value} className="!w-6 !h-6" />
          <span className="text-center">
            {item.content}
          </span>
        </div>)}
    </DropdownMenuContent>
  </DropdownMenu>
}