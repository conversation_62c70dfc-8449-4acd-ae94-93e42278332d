"use client"

import { useTranslations } from "next-intl"
import { useFaq } from "./use-faq"
import { useCallback, useEffect, useRef, useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { BoxSelectIcon, Coins, FileIcon, HelpCircle, Laptop, MessageCircle, Percent, Search, User2, Verified, X } from "lucide-react"
import useSearchParamWrapper from "@/hooks/use-search-param-wrapper"
import { useFaQStore } from "@/stores/faq.store"
import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper"
import { useDebounce } from "@/hooks/use-debounce"

export default function Sidebar() {
  const [open, setOpen] = useState(false)
  const sidebarRef = useRef<HTMLDivElement | null>(null)
  const [isInView, setIsInView] = useState(false)
  useEffect(() => {
    if (sidebarRef.current == null) return
    const observer = new IntersectionObserver(([entry]) => {
      setIsInView(entry.isIntersecting)
    },
      {
        root: null,
        rootMargin: "0px",
        threshold: 0.1
      }
    )
    observer.observe(sidebarRef.current)
    return () => {
      if (sidebarRef.current == null) return
      // eslint-disable-next-line react-hooks/exhaustive-deps
      observer.unobserve(sidebarRef.current)
    }

  }, [sidebarRef])
  return <div ref={sidebarRef}>
    <div className="w-fit h-fit max-lg:hidden">
      <SidebarContent />
    </div>
  </div>
}

export function SidebarContent() {
  const t = useTranslations("seeker")
  const { searchParams, removeQueryParam } = useSearchParamWrapper()
  const { filteringData } = useFaq()
  const { detail, setDetail } = useFaQStore(state => state)
  const [searchInput, setSearchInput] = useState(searchParams.get("search") || "")
  const debounce = useDebounce(searchInput)

  const handleSearch = useCallback((value: string) => {
    setDetail("")
    filteringData(value)
    if (value == "") {
      removeQueryParam(["search"])
      return
    }
  }, [filteringData, removeQueryParam, setDetail])
  useEffect(() => {
    if (debounce == "") return
    handleSearch(debounce)
  }, [debounce, handleSearch])


  const faqGroups = [
    { title: t('faq.group.usingThePlatform'), id: "using-platform", icon: <Laptop className="w-5 h-5" strokeWidth={1} /> },
    { title: t('faq.group.contactingPropertyOwner'), id: "contacting-owner", icon: <MessageCircle className="w-5 h-5" strokeWidth={1} /> },
    { title: t('faq.group.propertyVerificationAndSafety'), id: "property-verification", icon: <Verified className="w-5 h-5" strokeWidth={1} /> },
    { title: t('faq.group.paymentAndFee'), id: "payment-and-fee", icon: <Coins className="w-5 h-5" strokeWidth={1} /> },
    { title: t('faq.group.propertyVisitAndContract'), id: "property-visit", icon: <FileIcon className="w-5 h-5" strokeWidth={1} /> },
  ];
  return <div className="min-w-[300px]  h-fit sticky top-10 left-20 space-y-6">
    <div className="flex gap-1">
      <div className="border rounded-md flex items-center pl-2 h-9 w-full">
        <Search className="w-4 h-4 text-slate-400" />
        <Input placeholder={t('faq.searchPlaceholder')}
          value={searchInput}
          onChange={e => setSearchInput(e.target.value)}
          className='border-none focus:outline-none shadow-none focus-visible:ring-0'
          onKeyDown={(e) => {
            if (e.key == "Enter") {
              handleSearch(searchInput)
            }
          }} />
      </div>
      {
        searchParams.get("search") && <Button variant={"ghost"} size={"icon"} className="aspect-square" onClick={() => {
          handleSearch("")
          setSearchInput("")
        }}><X /></Button>
      }
    </div>
    <div>
      {faqGroups.map((item, idx) => <div
        key={idx}
        onClick={() => setDetail(item.id)}
        className={`lg:border-l max-sm:px-0  h-fit w-full flex items-start gap-2 p-4 cursor-pointer ${detail == item.id ? "lg:border-seekers-primary text-seekers-primary font-semibold" : "lg:border-seekers-text-lighter text-seekers-text-light"}`}>
        {item.icon}
        <p className="cursor-pointer">{item.title}</p>
      </div>
      )}
    </div>
  </div>
}