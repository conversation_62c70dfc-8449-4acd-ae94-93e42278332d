"use client"
import { useGetMyDetail } from "@/core/applications/queries/users/use-get-me"
import { baseUser, useUserStore } from "@/stores/user.store"
import { useEffect } from "react"

export default function SetupSeekersStore() {
  const { setSeekers, setRole } = useUserStore(state => state)
  const data = useGetMyDetail()
  useEffect(() => {
    if (data.data) {
      if (data.data.type == "OWNER") return
      setSeekers(data.data || baseUser)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data.data])
  return <></>
}