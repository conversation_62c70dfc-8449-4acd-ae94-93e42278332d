import { apiClient } from "@/core/client";
import {
  CreateTopUpCreditDto,
  GetAllTransactionDto,
  PromoDto,
  PutPaymentMethod,
} from "./dto";
import ssrApiClient from "@/core/ssr-client";
import { fetchMethod } from "@/core/utils/types";
const baseUrl = process.env.NEXT_PUBLIC_SERVICE_API;

export const getAllPackages = () => apiClient.get("packages/credit");
export const getPackageDetail = (id: string) => apiClient.get(`packages/${id}`);

export const postTopUpCredit = (data: CreateTopUpCreditDto) =>
  apiClient.post("credits/top-up", data);

export const getAllTransaction = (data: GetAllTransactionDto) =>
  apiClient.get(
    `transactions?search=${data.search}&page=${data.page}&per_page=${data.per_page}&type=${data.type || ""}${data.start_date ? "&start_date=" + data.start_date : ""}${data.end_date ? "&end_date=" + data.end_date : ""}`
  );
export const getDegtailTransaction = (code: string) =>
  apiClient.get(`transactions/${code}`);

export const postVerificationTopUpCredit = (id: string) =>
  apiClient.get(`credits/verification/${id}`);

export const checkTransaction = (id: string) =>
  apiClient.get(`transactions/${id}`);

export const postPromoCode = (data: PromoDto) =>
  apiClient.post("promos/apply", data);

export const putPaymentMethod = (data: PutPaymentMethod) =>
  apiClient.put("users/payment-methods", data);

export const ssrGetPaymentMethod = async <T>() => {
  const url = baseUrl + "/users/payment-methods";
  return await ssrApiClient<T>(url, fetchMethod.get, {
    cache: "no-store",
  });
};
