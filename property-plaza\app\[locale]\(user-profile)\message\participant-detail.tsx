"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import { useMessagingStore } from "@/stores/messaging.store"
import { ArrowLeft, ArrowUpLeft, ArrowUpRight, X } from "lucide-react"
import { useTranslations } from "next-intl"
import Image from "next/image"
import Link from "next/link"

export default function ParticipantDetail() {
  const t = useTranslations("seeker")
  const { currentLayout, setlayout } = useMessagingStore()

  const { participant } = useMessagingStore()
  return <div className={cn("relative",
    currentLayout == "detail-user" ?
      "md:rounded-lg w-full h-screen md:min-w-[300px] md:w-[300px] md:h-full bg-seekers-primary-light/10 flex flex-col items-center space-y-4 py-8 px-6 relative max-sm:fixed max-sm:top-0 max-sm:left-0 max-sm:z-50 max-sm:bg-background" :
      "hidden",
  )}>
    <Button
      variant={"ghost"}
      className="shadow-none absolute left-1 md:hidden top-[18px]"
      size={"icon"}
      onClick={() => setlayout("detail-chat")}
    >
      <ArrowLeft />
    </Button>
    <Button
      variant={"ghost"}
      className="shadow-none absolute max-sm:hidden right-3 top-1"
      size={"icon"}
      onClick={() => setlayout("detail-chat")}
    >
      <X />
    </Button>
    <ScrollArea className="w-full h-full ">
      <Avatar className={cn("w-28 h-28 rounded-xl bg-seekers-text-lighter mx-auto")}>
        <AvatarImage src={participant?.property?.image || participant?.image || ""} className="border" />
        <AvatarFallback className="bg-transparent text-2xl text-white">
          <span>{participant?.fullName[0][0]}{participant?.fullName[participant.fullName.length / 2]?.[0] || ""}</span>
        </AvatarFallback>
      </Avatar>
      <Link className="flex justify-center !mt-2 items-center" href={`/${participant?.property?.title?.replaceAll(" ", "-")}?code=${participant?.property?.id}`} target="_blank">
        <ArrowUpRight className="h-5 text-seekers-text w-5" />
      </Link>
      <div>
        <h2 className="text-base text-center font-semibold">
          {participant?.property?.title}
        </h2>
        <p className="text-center text-neutral-600">
          {participant?.fullName}
        </p>
        {participant?.middleman ?
          <>
            <p className="text-center text-seekers-text-lighter text-xs">{t('misc.representedBy')}</p>
            <p className="text-center">{participant.middleman.name}</p>
          </>
          :
          <></>
        }
      </div>
      <div className="h-fit w-full py-4 space-y-4">
        <h2 className="text-center text-seekers-text-light text-sm">
          {t('misc.otherProperties')}
        </h2>
        <div className="w-full space-y-2">

          {participant?.moreProperty?.map(item => <Link className="flex w-full gap-2" key={item.id} href={item.title + "?code=" + item.id} target="_blank">
            <Image
              className="h-10 rounded-full w-10 min-w-10" src={item.image || ""} alt="" width={48} height={48} />
            <p className="font-semibold line-clamp-2">{item.title}</p>
          </Link>)}
        </div>
      </div>
    </ScrollArea>
  </div>
}