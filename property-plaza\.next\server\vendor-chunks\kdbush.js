"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/kdbush";
exports.ids = ["vendor-chunks/kdbush"];
exports.modules = {

/***/ "(ssr)/./node_modules/kdbush/index.js":
/*!**************************************!*\
  !*** ./node_modules/kdbush/index.js ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ KDBush)\n/* harmony export */ });\n\nconst ARRAY_TYPES = [\n    Int8Array, Uint8Array, Uint8ClampedArray, Int16Array, Uint16Array,\n    Int32Array, Uint32Array, Float32Array, Float64Array\n];\n\n/** @typedef {Int8ArrayConstructor | Uint8ArrayConstructor | Uint8ClampedArrayConstructor | Int16ArrayConstructor | Uint16ArrayConstructor | Int32ArrayConstructor | Uint32ArrayConstructor | Float32ArrayConstructor | Float64ArrayConstructor} TypedArrayConstructor */\n\nconst VERSION = 1; // serialized format version\nconst HEADER_SIZE = 8;\n\nclass KDBush {\n\n    /**\n     * Creates an index from raw `ArrayBuffer` data.\n     * @param {ArrayBuffer} data\n     */\n    static from(data) {\n        if (!(data instanceof ArrayBuffer)) {\n            throw new Error('Data must be an instance of ArrayBuffer.');\n        }\n        const [magic, versionAndType] = new Uint8Array(data, 0, 2);\n        if (magic !== 0xdb) {\n            throw new Error('Data does not appear to be in a KDBush format.');\n        }\n        const version = versionAndType >> 4;\n        if (version !== VERSION) {\n            throw new Error(`Got v${version} data when expected v${VERSION}.`);\n        }\n        const ArrayType = ARRAY_TYPES[versionAndType & 0x0f];\n        if (!ArrayType) {\n            throw new Error('Unrecognized array type.');\n        }\n        const [nodeSize] = new Uint16Array(data, 2, 1);\n        const [numItems] = new Uint32Array(data, 4, 1);\n\n        return new KDBush(numItems, nodeSize, ArrayType, data);\n    }\n\n    /**\n     * Creates an index that will hold a given number of items.\n     * @param {number} numItems\n     * @param {number} [nodeSize=64] Size of the KD-tree node (64 by default).\n     * @param {TypedArrayConstructor} [ArrayType=Float64Array] The array type used for coordinates storage (`Float64Array` by default).\n     * @param {ArrayBuffer} [data] (For internal use only)\n     */\n    constructor(numItems, nodeSize = 64, ArrayType = Float64Array, data) {\n        if (isNaN(numItems) || numItems < 0) throw new Error(`Unpexpected numItems value: ${numItems}.`);\n\n        this.numItems = +numItems;\n        this.nodeSize = Math.min(Math.max(+nodeSize, 2), 65535);\n        this.ArrayType = ArrayType;\n        this.IndexArrayType = numItems < 65536 ? Uint16Array : Uint32Array;\n\n        const arrayTypeIndex = ARRAY_TYPES.indexOf(this.ArrayType);\n        const coordsByteSize = numItems * 2 * this.ArrayType.BYTES_PER_ELEMENT;\n        const idsByteSize = numItems * this.IndexArrayType.BYTES_PER_ELEMENT;\n        const padCoords = (8 - idsByteSize % 8) % 8;\n\n        if (arrayTypeIndex < 0) {\n            throw new Error(`Unexpected typed array class: ${ArrayType}.`);\n        }\n\n        if (data && (data instanceof ArrayBuffer)) { // reconstruct an index from a buffer\n            this.data = data;\n            this.ids = new this.IndexArrayType(this.data, HEADER_SIZE, numItems);\n            this.coords = new this.ArrayType(this.data, HEADER_SIZE + idsByteSize + padCoords, numItems * 2);\n            this._pos = numItems * 2;\n            this._finished = true;\n        } else { // initialize a new index\n            this.data = new ArrayBuffer(HEADER_SIZE + coordsByteSize + idsByteSize + padCoords);\n            this.ids = new this.IndexArrayType(this.data, HEADER_SIZE, numItems);\n            this.coords = new this.ArrayType(this.data, HEADER_SIZE + idsByteSize + padCoords, numItems * 2);\n            this._pos = 0;\n            this._finished = false;\n\n            // set header\n            new Uint8Array(this.data, 0, 2).set([0xdb, (VERSION << 4) + arrayTypeIndex]);\n            new Uint16Array(this.data, 2, 1)[0] = nodeSize;\n            new Uint32Array(this.data, 4, 1)[0] = numItems;\n        }\n    }\n\n    /**\n     * Add a point to the index.\n     * @param {number} x\n     * @param {number} y\n     * @returns {number} An incremental index associated with the added item (starting from `0`).\n     */\n    add(x, y) {\n        const index = this._pos >> 1;\n        this.ids[index] = index;\n        this.coords[this._pos++] = x;\n        this.coords[this._pos++] = y;\n        return index;\n    }\n\n    /**\n     * Perform indexing of the added points.\n     */\n    finish() {\n        const numAdded = this._pos >> 1;\n        if (numAdded !== this.numItems) {\n            throw new Error(`Added ${numAdded} items when expected ${this.numItems}.`);\n        }\n        // kd-sort both arrays for efficient search\n        sort(this.ids, this.coords, this.nodeSize, 0, this.numItems - 1, 0);\n\n        this._finished = true;\n        return this;\n    }\n\n    /**\n     * Search the index for items within a given bounding box.\n     * @param {number} minX\n     * @param {number} minY\n     * @param {number} maxX\n     * @param {number} maxY\n     * @returns {number[]} An array of indices correponding to the found items.\n     */\n    range(minX, minY, maxX, maxY) {\n        if (!this._finished) throw new Error('Data not yet indexed - call index.finish().');\n\n        const {ids, coords, nodeSize} = this;\n        const stack = [0, ids.length - 1, 0];\n        const result = [];\n\n        // recursively search for items in range in the kd-sorted arrays\n        while (stack.length) {\n            const axis = stack.pop() || 0;\n            const right = stack.pop() || 0;\n            const left = stack.pop() || 0;\n\n            // if we reached \"tree node\", search linearly\n            if (right - left <= nodeSize) {\n                for (let i = left; i <= right; i++) {\n                    const x = coords[2 * i];\n                    const y = coords[2 * i + 1];\n                    if (x >= minX && x <= maxX && y >= minY && y <= maxY) result.push(ids[i]);\n                }\n                continue;\n            }\n\n            // otherwise find the middle index\n            const m = (left + right) >> 1;\n\n            // include the middle item if it's in range\n            const x = coords[2 * m];\n            const y = coords[2 * m + 1];\n            if (x >= minX && x <= maxX && y >= minY && y <= maxY) result.push(ids[m]);\n\n            // queue search in halves that intersect the query\n            if (axis === 0 ? minX <= x : minY <= y) {\n                stack.push(left);\n                stack.push(m - 1);\n                stack.push(1 - axis);\n            }\n            if (axis === 0 ? maxX >= x : maxY >= y) {\n                stack.push(m + 1);\n                stack.push(right);\n                stack.push(1 - axis);\n            }\n        }\n\n        return result;\n    }\n\n    /**\n     * Search the index for items within a given radius.\n     * @param {number} qx\n     * @param {number} qy\n     * @param {number} r Query radius.\n     * @returns {number[]} An array of indices correponding to the found items.\n     */\n    within(qx, qy, r) {\n        if (!this._finished) throw new Error('Data not yet indexed - call index.finish().');\n\n        const {ids, coords, nodeSize} = this;\n        const stack = [0, ids.length - 1, 0];\n        const result = [];\n        const r2 = r * r;\n\n        // recursively search for items within radius in the kd-sorted arrays\n        while (stack.length) {\n            const axis = stack.pop() || 0;\n            const right = stack.pop() || 0;\n            const left = stack.pop() || 0;\n\n            // if we reached \"tree node\", search linearly\n            if (right - left <= nodeSize) {\n                for (let i = left; i <= right; i++) {\n                    if (sqDist(coords[2 * i], coords[2 * i + 1], qx, qy) <= r2) result.push(ids[i]);\n                }\n                continue;\n            }\n\n            // otherwise find the middle index\n            const m = (left + right) >> 1;\n\n            // include the middle item if it's in range\n            const x = coords[2 * m];\n            const y = coords[2 * m + 1];\n            if (sqDist(x, y, qx, qy) <= r2) result.push(ids[m]);\n\n            // queue search in halves that intersect the query\n            if (axis === 0 ? qx - r <= x : qy - r <= y) {\n                stack.push(left);\n                stack.push(m - 1);\n                stack.push(1 - axis);\n            }\n            if (axis === 0 ? qx + r >= x : qy + r >= y) {\n                stack.push(m + 1);\n                stack.push(right);\n                stack.push(1 - axis);\n            }\n        }\n\n        return result;\n    }\n}\n\n/**\n * @param {Uint16Array | Uint32Array} ids\n * @param {InstanceType<TypedArrayConstructor>} coords\n * @param {number} nodeSize\n * @param {number} left\n * @param {number} right\n * @param {number} axis\n */\nfunction sort(ids, coords, nodeSize, left, right, axis) {\n    if (right - left <= nodeSize) return;\n\n    const m = (left + right) >> 1; // middle index\n\n    // sort ids and coords around the middle index so that the halves lie\n    // either left/right or top/bottom correspondingly (taking turns)\n    select(ids, coords, m, left, right, axis);\n\n    // recursively kd-sort first half and second half on the opposite axis\n    sort(ids, coords, nodeSize, left, m - 1, 1 - axis);\n    sort(ids, coords, nodeSize, m + 1, right, 1 - axis);\n}\n\n/**\n * Custom Floyd-Rivest selection algorithm: sort ids and coords so that\n * [left..k-1] items are smaller than k-th item (on either x or y axis)\n * @param {Uint16Array | Uint32Array} ids\n * @param {InstanceType<TypedArrayConstructor>} coords\n * @param {number} k\n * @param {number} left\n * @param {number} right\n * @param {number} axis\n */\nfunction select(ids, coords, k, left, right, axis) {\n\n    while (right > left) {\n        if (right - left > 600) {\n            const n = right - left + 1;\n            const m = k - left + 1;\n            const z = Math.log(n);\n            const s = 0.5 * Math.exp(2 * z / 3);\n            const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n            const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n            const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n            select(ids, coords, k, newLeft, newRight, axis);\n        }\n\n        const t = coords[2 * k + axis];\n        let i = left;\n        let j = right;\n\n        swapItem(ids, coords, left, k);\n        if (coords[2 * right + axis] > t) swapItem(ids, coords, left, right);\n\n        while (i < j) {\n            swapItem(ids, coords, i, j);\n            i++;\n            j--;\n            while (coords[2 * i + axis] < t) i++;\n            while (coords[2 * j + axis] > t) j--;\n        }\n\n        if (coords[2 * left + axis] === t) swapItem(ids, coords, left, j);\n        else {\n            j++;\n            swapItem(ids, coords, j, right);\n        }\n\n        if (j <= k) left = j + 1;\n        if (k <= j) right = j - 1;\n    }\n}\n\n/**\n * @param {Uint16Array | Uint32Array} ids\n * @param {InstanceType<TypedArrayConstructor>} coords\n * @param {number} i\n * @param {number} j\n */\nfunction swapItem(ids, coords, i, j) {\n    swap(ids, i, j);\n    swap(coords, 2 * i, 2 * j);\n    swap(coords, 2 * i + 1, 2 * j + 1);\n}\n\n/**\n * @param {InstanceType<TypedArrayConstructor>} arr\n * @param {number} i\n * @param {number} j\n */\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\n/**\n * @param {number} ax\n * @param {number} ay\n * @param {number} bx\n * @param {number} by\n */\nfunction sqDist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kdbush/index.js\n");

/***/ })

};
;