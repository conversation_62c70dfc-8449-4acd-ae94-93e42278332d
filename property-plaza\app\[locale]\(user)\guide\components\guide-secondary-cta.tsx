"use client"

import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, MapPin, Shield, Star, Eye } from "lucide-react";
import Image from "next/image";

export default function GuideSecondaryCTA() {
  const t = useTranslations("guide");

  const handleBrowseListings = () => {
    // Track CTA click
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'browse_listings_cta_click', {
        event_category: 'Lead Magnet',
        event_label: 'Secondary CTA',
        value: 1
      });
    }
    
    // Open main site in new tab
    window.open('/', '_blank');
  };

  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 to-gray-800 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - CTA Content */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h2 className="text-4xl sm:text-5xl font-bold leading-tight">
                Ready to Find Your 
                <span className="text-teal-400"> Perfect Home</span>?
              </h2>
              
              <p className="text-xl text-gray-300 leading-relaxed">
                Now that you have the insider knowledge, browse our curated collection 
                of verified properties. Every listing is personally inspected and legally verified.
              </p>
            </div>

            {/* Features */}
            <div className="grid sm:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-seekers-primary rounded-full flex items-center justify-center flex-shrink-0">
                  <Shield className="h-5 w-5 text-white" />
                </div>
                <div>
                  <div className="font-semibold">Verified Listings</div>
                  <div className="text-sm text-gray-400">Every property is inspected</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-seekers-primary rounded-full flex items-center justify-center flex-shrink-0">
                  <MapPin className="h-5 w-5 text-white" />
                </div>
                <div>
                  <div className="font-semibold">Direct Contact</div>
                  <div className="text-sm text-gray-400">Direct contact with owners</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-seekers-primary rounded-full flex items-center justify-center flex-shrink-0">
                  <Star className="h-5 w-5 text-white" />
                </div>
                <div>
                  <div className="font-semibold">Fair Pricing</div>
                  <div className="text-sm text-gray-400">No inflated expat rates</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-seekers-primary rounded-full flex items-center justify-center flex-shrink-0">
                  <Eye className="h-5 w-5 text-white" />
                </div>
                <div>
                  <div className="font-semibold">Real Photos</div>
                  <div className="text-sm text-gray-400">What you see is what you get</div>
                </div>
              </div>
            </div>

            {/* CTA Button */}
            <div className="pt-4">
              <Button
                onClick={handleBrowseListings}
                size="lg"
                className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                Browse Verified Listings
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              
              <p className="text-sm text-gray-400 mt-3">
                Free to browse • No registration required
              </p>
            </div>
          </div>

          {/* Right Column - Screenshot Preview */}
          <div className="relative">
            {/* Main Screenshot */}
            <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden transform rotate-2 hover:rotate-0 transition-transform duration-500">
              {/* Browser Header */}
              <div className="bg-gray-100 px-4 py-3 flex items-center gap-2">
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                </div>
                <div className="flex-1 bg-white rounded px-3 py-1 text-xs text-gray-600 ml-4">
                  property-plaza.com
                </div>
              </div>
              
              {/* Screenshot Content */}
              <div className="p-6 bg-gradient-to-br from-teal-50 to-blue-50 min-h-[400px]">
                {/* Header */}
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    Find Your Perfect Property
                  </h3>
                  <p className="text-gray-600">Verified listings across Bali</p>
                </div>
                
                {/* Search Bar */}
                <div className="bg-white rounded-lg p-4 shadow-sm mb-6">
                  <div className="flex items-center gap-2 text-gray-400">
                    <MapPin className="h-4 w-4" />
                    <span className="text-sm">Search by location, price, or type...</span>
                  </div>
                </div>
                
                {/* Property Cards */}
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="bg-white rounded-lg p-4 shadow-sm flex gap-4">
                      <div className="w-20 h-16 bg-gray-200 rounded-lg flex-shrink-0"></div>
                      <div className="flex-1">
                        <div className="h-3 bg-gray-200 rounded mb-2"></div>
                        <div className="h-2 bg-gray-100 rounded w-2/3 mb-2"></div>
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 bg-teal-200 rounded-full"></div>
                          <div className="h-2 bg-gray-100 rounded w-1/3"></div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="h-3 bg-teal-200 rounded w-16 mb-1"></div>
                        <div className="h-2 bg-gray-100 rounded w-12"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Floating Elements */}
            <div className="absolute -top-6 -left-6 w-16 h-16 bg-seekers-primary rounded-full flex items-center justify-center shadow-lg animate-pulse">
              <Shield className="h-8 w-8 text-white" />
            </div>
            
            <div className="absolute -bottom-4 -right-4 bg-white rounded-lg p-3 shadow-lg">
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-gray-600 font-medium">1,247 Verified</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Trust Bar */}
        <div className="mt-16 pt-8 border-t border-gray-700">
          <div className="text-center">
            <p className="text-gray-400 mb-4">
              Join thousands of expats who found their perfect home through Property Plaza
            </p>
            <div className="flex justify-center items-center gap-8 text-sm text-gray-500">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>1,200+ Properties</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>100% Verified</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>Legal Compliance</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
