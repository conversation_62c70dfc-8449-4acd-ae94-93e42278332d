import { AnimatePresence } from "framer-motion";
import { ComponentProps } from "react";
import { cn } from "@/lib/utils";

export default function SeekersNavbarContainer({ ...rest }: ComponentProps<"nav">) {
  return <AnimatePresence>
    <nav {...rest} className={cn("w-full max-xl:space-y-4 border-b relative shadow-sm shadow-neutral-600/20 bg-white md:h-[90px] lg:h-[114px]"
      , rest.className
    )}>
    </nav>
  </AnimatePresence>
}