"use client"

import { But<PERSON> } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { ArrowDown, Download } from "lucide-react";

export default function GuideHeroSection() {
  const t = useTranslations("guide");

  const scrollToEmailForm = () => {
    const emailSection = document.getElementById('email-capture-section');
    emailSection?.scrollIntoView({ behavior: 'smooth' });
    
    // Track CTA click
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'guide_download_cta_click', {
        event_category: 'Lead Magnet',
        event_label: 'Hero CTA',
        value: 1
      });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-[140px]">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="https://images.pexels.com/photos/2166553/pexels-photo-2166553.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=1"
          alt="Beautiful Bali villa with rice field view"
          fill
          className="object-cover"
          priority
          sizes="100vw"
          quality={85}
          placeholder="blur"
          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
        />
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/40" />
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center min-h-screen py-20 lg:py-0">
          {/* Left Column - Text Content */}
          <div className="space-y-6 lg:space-y-8 order-2 lg:order-1">
            <div className="space-y-4 lg:space-y-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                {t('hero.title')}{" "}
                <span className="text-red-400">{t('hero.titleHighlight')}</span>
              </h1>

              <p className="text-lg sm:text-xl lg:text-2xl text-gray-200 leading-relaxed max-w-2xl mx-auto lg:mx-0">
                {t('hero.subtitle')}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button
                onClick={scrollToEmailForm}
                size="lg"
                className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                <Download className="mr-2 h-5 w-5" />
                {t('hero.cta.primary')}
              </Button>

              <Button
                variant="outline"
                onClick={scrollToEmailForm}
                size="lg"
                className="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 text-lg font-semibold rounded-lg transition-all duration-300 bg-transparent"
              >
                <ArrowDown className="mr-2 h-5 w-5" />
                {t('hero.cta.secondary')}
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-gray-300">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>{t('hero.trustIndicators.free')}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>{t('hero.trustIndicators.noSpam')}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>{t('hero.trustIndicators.instant')}</span>
              </div>
            </div>
          </div>

          {/* Right Column - Guide Mockup */}
          <div className="flex justify-center lg:justify-end order-1 lg:order-2">
            <div className="relative">
              {/* iPad/Phone Mockup */}
              <div className="relative w-72 sm:w-80 h-80 sm:h-96 bg-white rounded-2xl shadow-2xl p-3 sm:p-4 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                <div className="w-full h-full bg-gradient-to-br from-seekers-primary-lightest to-neutral-lightest rounded-xl flex flex-col items-center justify-center p-4 sm:p-6">
                  <div className="w-12 sm:w-16 h-12 sm:h-16 bg-seekers-primary rounded-full flex items-center justify-center mb-3 sm:mb-4">
                    <Download className="h-6 sm:h-8 w-6 sm:w-8 text-white" />
                  </div>
                  <h3 className="text-gray-800 font-bold text-base sm:text-lg text-center mb-2">
                    Bali Housing Guide 2025
                  </h3>
                  <p className="text-gray-600 text-xs sm:text-sm text-center mb-3 sm:mb-4 px-2">
                    Everything you need to know about finding safe, legal housing in Bali
                  </p>
                  <div className="w-full h-24 sm:h-32 bg-white rounded-lg shadow-inner flex items-center justify-center">
                    <span className="text-gray-400 text-xs">PDF Preview</span>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-2 sm:-top-4 -right-2 sm:-right-4 w-10 sm:w-12 h-10 sm:h-12 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                <span className="text-xs font-bold">FREE</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <ArrowDown className="h-6 w-6 text-white/70" />
      </div>
    </section>
  );
}
