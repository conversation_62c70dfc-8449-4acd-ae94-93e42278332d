"use client"

import { useTranslations } from "next-intl";
import { Shield, MapPin, DollarSign, FileCheck, AlertTriangle } from "lucide-react";

export default function GuideValueProposition() {
  const t = useTranslations("guide");

  const benefits = [
    {
      icon: Shield,
      title: "Avoid Common Scams",
      description: "Learn the red flags and warning signs that indicate fake brokers, fraudulent listings, and rental scams targeting expats.",
      color: "text-red-500 bg-red-50"
    },
    {
      icon: MapPin,
      title: "How the Bali Housing Market Really Works",
      description: "Understand the local rental market, seasonal pricing, and the difference between tourist and local rental rates.",
      color: "text-blue-500 bg-blue-50"
    },
    {
      icon: FileCheck,
      title: "Zoning Explained Simply",
      description: "Navigate Bali's complex zoning laws and understand what areas are legal for long-term rentals and what to avoid.",
      color: "text-seekers-primary bg-seekers-primary-lightest"
    },
    {
      icon: AlertTriangle,
      title: "Rental Safety Checklist",
      description: "Essential safety checks, legal requirements, and documentation you need before signing any rental agreement.",
      color: "text-purple-500 bg-purple-50"
    }
  ];

  return (
    <section className="py-20 bg-seekers-foreground/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
            What's Inside the Guide?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Everything you need to know to find safe, legal, and fairly-priced housing in Bali. 
            Written by expats, for expats.
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {benefits.map((benefit, index) => {
            const IconComponent = benefit.icon;
            return (
              <div
                key={index}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
              >
                <div className={`w-16 h-16 rounded-2xl ${benefit.color} flex items-center justify-center mb-6`}>
                  <IconComponent className="h-8 w-8" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  {benefit.title}
                </h3>
                
                <p className="text-gray-600 leading-relaxed">
                  {benefit.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Additional Value Props */}
        <div className="bg-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">
                Why This Guide Exists
              </h3>
              <div className="space-y-4 text-gray-600">
                <p className="leading-relaxed">
                  We've seen too many expats get burned by fake brokers, overpriced rentals, 
                  and legal issues that could have been easily avoided.
                </p>
                <p className="leading-relaxed">
                  This guide compiles years of experience from Property Plaza's team and 
                  hundreds of successful expat relocations to Bali.
                </p>
                <p className="leading-relaxed font-semibold text-gray-900">
                  Don't learn these lessons the hard way. Get the insider knowledge upfront.
                </p>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-seekers-primary-lightest to-neutral-lightest rounded-2xl p-8">
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-seekers-primary rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold text-sm">1</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Download the Guide</h4>
                    <p className="text-sm text-gray-600">Get instant access to our comprehensive housing guide</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-seekers-primary rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold text-sm">2</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Apply the Knowledge</h4>
                    <p className="text-sm text-gray-600">Use our checklists and tips during your housing search</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-seekers-primary rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold text-sm">3</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Find Your Perfect Home</h4>
                    <p className="text-sm text-gray-600">Browse verified listings on Property Plaza with confidence</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
