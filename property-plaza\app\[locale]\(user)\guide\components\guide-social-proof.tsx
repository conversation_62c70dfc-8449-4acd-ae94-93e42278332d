"use client"

import { useTranslations } from "next-intl";
import { Star, Quote } from "lucide-react";
import Image from "next/image";

export default function GuideSocialProof() {
  const t = useTranslations("guide");

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Digital Nomad from Netherlands",
      avatar: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2",
      quote: "Finally found a trustworthy source for Bali housing. The guide saved me from a €2000 scam! The zoning section alone is worth gold.",
      rating: 5
    },
    {
      name: "<PERSON>",
      role: "Remote Developer from Germany",
      avatar: "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2",
      quote: "Wish I had this guide before my first move. Would have saved months of stress and bad landlords. Now I know exactly what to look for.",
      rating: 5
    },
    {
      name: "<PERSON>",
      role: "Entrepreneur from Australia",
      avatar: "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2",
      quote: "The zoning section alone is worth gold. No more confusion about what I can actually rent. Property Plaza's team really knows their stuff.",
      rating: 5
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Layout */}
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Header */}
          <div>
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Trusted by Expats Worldwide
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Join thousands of expats who've successfully found their perfect home in Bali
              using our insider knowledge and verified listings.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-2 gap-6">
              <div>
                <div className="text-2xl font-bold text-seekers-primary mb-1">5,000+</div>
                <div className="text-sm text-gray-600">Guides Downloaded</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-seekers-primary mb-1">2,500+</div>
                <div className="text-sm text-gray-600">Happy Expats</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-seekers-primary mb-1">98%</div>
                <div className="text-sm text-gray-600">Success Rate</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-seekers-primary mb-1">4.9/5</div>
                <div className="text-sm text-gray-600">Average Rating</div>
              </div>
            </div>
          </div>

          {/* Right Column - Testimonials */}
          <div className="space-y-4">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300"
              >
                {/* Rating */}
                <div className="flex items-center gap-1 mb-3">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>

                {/* Quote */}
                <blockquote className="text-gray-700 mb-4 text-sm leading-relaxed">
                  "{testimonial.quote}"
                </blockquote>

                {/* Author */}
                <div className="flex items-center gap-3">
                  <div className="relative w-8 h-8 rounded-full overflow-hidden">
                    <Image
                      src={testimonial.avatar}
                      alt={`${testimonial.name} avatar`}
                      fill
                      className="object-cover"
                      sizes="32px"
                    />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 text-sm">
                      {testimonial.name}
                    </div>
                    <div className="text-xs text-gray-600">
                      {testimonial.role}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
