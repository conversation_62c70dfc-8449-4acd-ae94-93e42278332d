import SearchLayoutContent from "../search-layout-content"
import SearchFilterAndCategory from "../search-filter-and-category"
import ContentSearch from "./content"
import { SearchParams } from "@/hooks/use-seekers-filter"
import { filterTitles } from "@/lib/constanta/constant"
import { Metadata } from "next"
import { getTranslations } from "next-intl/server"
import { listingCategory, ListingCategory } from "@/core/domain/listing/listing-seekers"
import { ViewMode } from "@/stores/seekers-search-map-utils"
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api"
import { BaseMetadataProps } from "@/types/base"
import { searchUrl } from "@/lib/constanta/route"

type BaseProps = {
  params: { query: string }
  searchParams: { [key: string]: string | string[] | undefined }
}
// TODO:
// Add locale
export async function generateMetadata(
  { params, searchParams }: BaseMetadataProps<{ query: string }>,
): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const categoryParam = searchParams.t as string
  if (!categoryParam) {
    return {
      title: t('metadata.searchPage.title'),
      description: t('metadata.searchPage.description'),
      alternates: {
        canonical: "https://property-plaza.com/en" + `${searchUrl}/${params.query}`,
        languages: {
          "id": process.env.USER_DOMAIN + `id${searchUrl}/${params.query}`,
          "en": process.env.USER_DOMAIN + `en${searchUrl}/${params.query}`,
        },

      }
    }
  }
  const categories: ListingCategory[] = categoryParam.split(",")
  const formattedCategory = []
  for (const category of categories) {
    switch (category) {
      case listingCategory.villa:
        formattedCategory.push(t("listing.category.villa"))
        continue
      case listingCategory.apartment:
        formattedCategory.push(t("listing.category.apartment"))
        continue
      case listingCategory.cafeOrRestaurants:
        formattedCategory.push(t("listing.category.cafeAndRestaurent"))
        continue
      case listingCategory.commercialSpace:
        formattedCategory.push(t("listing.category.commercial"))
        continue
      case listingCategory.guestHouse:
        formattedCategory.push(t("listing.category.guestHouse"))
        continue
      case listingCategory.homestay:
        formattedCategory.push(t("listing.category.homestay"))
        continue
      case listingCategory.lands:
        formattedCategory.push(t("listing.category.land"))
        continue
      case listingCategory.offices:
        formattedCategory.push(t("listing.category.office"))
        continue
      case listingCategory.rooms:
        formattedCategory.push(t("listing.category.rooms"))
        continue
      case listingCategory.shellAndCore:
        formattedCategory.push(t("listing.category.shellAndCore"))
        continue
      case listingCategory.shops:
        formattedCategory.push(t("listing.category.shops"))
        continue
      case listingCategory.villa:
        formattedCategory.push(t("listing.category.villa"))
        continue
      default:
        formattedCategory.push(t("misc.allProperty"))
    }
  }
  const title = t("metadata.searchPage.multipleCategoryOrLocation.title", { category: formattedCategory.toString(), location: "Bali" })
  return {
    title: title,
    description: t('metadata.searchPage.description')
  }
}


export default async function SearchPropertyPage({ params, searchParams }: { params: { query: string }, searchParams: Record<string, string | string[] | undefined> }) {

  const searchParam: SearchParams = {
    page: searchParams.page as string || "1",
    perPage: searchParams[filterTitles.viewMode] as ViewMode == "map" ? "999" : "15",
    query: params.query || "all",
    types: searchParams[filterTitles.type] as string || "all",
    maxPrice: +(searchParams[filterTitles.maxPrice] as string) || undefined,
    minPrice: +(searchParams[filterTitles.minPrice] as string) || undefined,
    yearsOfBuilding: searchParams[filterTitles.yearsOfBuild] as string || undefined,
    bedroomTotal: +(searchParams[filterTitles.bedroomTotal] as string) || undefined,
    bathroomTotal: +(searchParams[filterTitles.bathroomTotal] as string) || undefined,
    feature: (searchParams[filterTitles.feature] as string) || undefined,
    rentalOffers: (searchParams[filterTitles.rentalOffer] as string) || undefined,
    sellingPoints: (searchParams[filterTitles.propertyCondition] as string) || undefined,
    lat: searchParams.lat as string || undefined,
    lng: searchParams.lng as string || undefined,
    sortBy: searchParams.sortBy as string || undefined,
    buildingLargest: +(searchParams[filterTitles.buildingLargest] as string) || undefined,
    buildingSmallest: +(searchParams[filterTitles.buildingSmallest] as string) || undefined,
    gardenLargest: +(searchParams[filterTitles.gardenLargest] as string) || undefined,
    gardenSmallest: +(searchParams[filterTitles.gardenSmallest] as string) || undefined,
    LandLargest: +(searchParams[filterTitles.landLargest] as string) || undefined,
    LandSmallest: +(searchParams[filterTitles.landSmallest] as string) || undefined,
    electricity: searchParams[filterTitles.electircity] as string || undefined,
    parkingOption: searchParams[filterTitles.parking] as string || undefined,
    poolOption: searchParams[filterTitles.swimmingPool] as string || undefined,
    typeLiving: searchParams[filterTitles.typeLiving] as string || undefined,
    furnishingOption: searchParams[filterTitles.furnished] as string || undefined,
    propertyOfView: searchParams[filterTitles.view] as string || undefined,
    propertyLocation: searchParams[filterTitles.propertyLocation] as string || undefined,
    minimumContract: searchParams[filterTitles.minimumContract] as string || undefined,
    zoom: searchParams[filterTitles.zoom] as string || undefined
  }
  const conversionRates = await getCurrencyConversion()

  const { query } = params
  return <>
    <SearchFilterAndCategory query={query} types={searchParam.types} conversions={conversionRates.data} />
    <SearchLayoutContent conversions={conversionRates.data}>
      <ContentSearch {...searchParam} conversions={conversionRates.data} />
    </SearchLayoutContent>
  </>
}