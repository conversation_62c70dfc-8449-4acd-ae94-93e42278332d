"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@googlemaps";
exports.ids = ["vendor-chunks/@googlemaps"];
exports.modules = {

/***/ "(ssr)/./node_modules/@googlemaps/markerclusterer/dist/index.esm.js":
/*!********************************************************************!*\
  !*** ./node_modules/@googlemaps/markerclusterer/dist/index.esm.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbstractAlgorithm: () => (/* binding */ AbstractAlgorithm),\n/* harmony export */   AbstractViewportAlgorithm: () => (/* binding */ AbstractViewportAlgorithm),\n/* harmony export */   Cluster: () => (/* binding */ Cluster),\n/* harmony export */   ClusterStats: () => (/* binding */ ClusterStats),\n/* harmony export */   DefaultRenderer: () => (/* binding */ DefaultRenderer),\n/* harmony export */   GridAlgorithm: () => (/* binding */ GridAlgorithm),\n/* harmony export */   MarkerClusterer: () => (/* binding */ MarkerClusterer),\n/* harmony export */   MarkerClustererEvents: () => (/* binding */ MarkerClustererEvents),\n/* harmony export */   MarkerUtils: () => (/* binding */ MarkerUtils),\n/* harmony export */   NoopAlgorithm: () => (/* binding */ NoopAlgorithm),\n/* harmony export */   SuperClusterAlgorithm: () => (/* binding */ SuperClusterAlgorithm),\n/* harmony export */   SuperClusterViewportAlgorithm: () => (/* binding */ SuperClusterViewportAlgorithm),\n/* harmony export */   defaultOnClusterClickHandler: () => (/* binding */ defaultOnClusterClickHandler),\n/* harmony export */   distanceBetweenPoints: () => (/* binding */ distanceBetweenPoints),\n/* harmony export */   extendBoundsToPaddedViewport: () => (/* binding */ extendBoundsToPaddedViewport),\n/* harmony export */   extendPixelBounds: () => (/* binding */ extendPixelBounds),\n/* harmony export */   filterMarkersToPaddedViewport: () => (/* binding */ filterMarkersToPaddedViewport),\n/* harmony export */   getPaddedViewport: () => (/* binding */ getPaddedViewport),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   pixelBoundsToLatLngBounds: () => (/* binding */ pixelBoundsToLatLngBounds)\n/* harmony export */ });\n/* harmony import */ var fast_deep_equal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fast-deep-equal */ \"(ssr)/./node_modules/fast-deep-equal/index.js\");\n/* harmony import */ var fast_deep_equal__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fast_deep_equal__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var supercluster__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! supercluster */ \"(ssr)/./node_modules/supercluster/index.js\");\n\n\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\n\n/**\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * util class that creates a common set of convenience functions to wrap\n * shared behavior of Advanced Markers and Markers.\n */\nclass MarkerUtils {\n    static isAdvancedMarkerAvailable(map) {\n        return (google.maps.marker &&\n            map.getMapCapabilities().isAdvancedMarkersAvailable === true);\n    }\n    static isAdvancedMarker(marker) {\n        return (google.maps.marker &&\n            marker instanceof google.maps.marker.AdvancedMarkerElement);\n    }\n    static setMap(marker, map) {\n        if (this.isAdvancedMarker(marker)) {\n            marker.map = map;\n        }\n        else {\n            marker.setMap(map);\n        }\n    }\n    static getPosition(marker) {\n        // SuperClusterAlgorithm.calculate expects a LatLng instance so we fake it for Adv Markers\n        if (this.isAdvancedMarker(marker)) {\n            if (marker.position) {\n                if (marker.position instanceof google.maps.LatLng) {\n                    return marker.position;\n                }\n                // since we can't cast to LatLngLiteral for reasons =(\n                if (marker.position.lat && marker.position.lng) {\n                    return new google.maps.LatLng(marker.position.lat, marker.position.lng);\n                }\n            }\n            return new google.maps.LatLng(null);\n        }\n        return marker.getPosition();\n    }\n    static getVisible(marker) {\n        if (this.isAdvancedMarker(marker)) {\n            /**\n             * Always return true for Advanced Markers because the clusterer\n             * uses getVisible as a way to count legacy markers not as an actual\n             * indicator of visibility for some reason. Even when markers are hidden\n             * Marker.getVisible returns `true` and this is used to set the marker count\n             * on the cluster. See the behavior of Cluster.count\n             */\n            return true;\n        }\n        return marker.getVisible();\n    }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass Cluster {\n    constructor({ markers, position }) {\n        this.markers = markers;\n        if (position) {\n            if (position instanceof google.maps.LatLng) {\n                this._position = position;\n            }\n            else {\n                this._position = new google.maps.LatLng(position);\n            }\n        }\n    }\n    get bounds() {\n        if (this.markers.length === 0 && !this._position) {\n            return;\n        }\n        const bounds = new google.maps.LatLngBounds(this._position, this._position);\n        for (const marker of this.markers) {\n            bounds.extend(MarkerUtils.getPosition(marker));\n        }\n        return bounds;\n    }\n    get position() {\n        return this._position || this.bounds.getCenter();\n    }\n    /**\n     * Get the count of **visible** markers.\n     */\n    get count() {\n        return this.markers.filter((m) => MarkerUtils.getVisible(m)).length;\n    }\n    /**\n     * Add a marker to the cluster.\n     */\n    push(marker) {\n        this.markers.push(marker);\n    }\n    /**\n     * Cleanup references and remove marker from map.\n     */\n    delete() {\n        if (this.marker) {\n            MarkerUtils.setMap(this.marker, null);\n            this.marker = undefined;\n        }\n        this.markers.length = 0;\n    }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns the markers visible in a padded map viewport\n *\n * @param map\n * @param mapCanvasProjection\n * @param markers The list of marker to filter\n * @param viewportPaddingPixels The padding in pixel\n * @returns The list of markers in the padded viewport\n */\nconst filterMarkersToPaddedViewport = (map, mapCanvasProjection, markers, viewportPaddingPixels) => {\n    const extendedMapBounds = extendBoundsToPaddedViewport(map.getBounds(), mapCanvasProjection, viewportPaddingPixels);\n    return markers.filter((marker) => extendedMapBounds.contains(MarkerUtils.getPosition(marker)));\n};\n/**\n * Extends a bounds by a number of pixels in each direction\n */\nconst extendBoundsToPaddedViewport = (bounds, projection, numPixels) => {\n    const { northEast, southWest } = latLngBoundsToPixelBounds(bounds, projection);\n    const extendedPixelBounds = extendPixelBounds({ northEast, southWest }, numPixels);\n    return pixelBoundsToLatLngBounds(extendedPixelBounds, projection);\n};\n/**\n * Gets the extended bounds as a bbox [westLng, southLat, eastLng, northLat]\n */\nconst getPaddedViewport = (bounds, projection, pixels) => {\n    const extended = extendBoundsToPaddedViewport(bounds, projection, pixels);\n    const ne = extended.getNorthEast();\n    const sw = extended.getSouthWest();\n    return [sw.lng(), sw.lat(), ne.lng(), ne.lat()];\n};\n/**\n * Returns the distance between 2 positions.\n *\n * @hidden\n */\nconst distanceBetweenPoints = (p1, p2) => {\n    const R = 6371; // Radius of the Earth in km\n    const dLat = ((p2.lat - p1.lat) * Math.PI) / 180;\n    const dLon = ((p2.lng - p1.lng) * Math.PI) / 180;\n    const sinDLat = Math.sin(dLat / 2);\n    const sinDLon = Math.sin(dLon / 2);\n    const a = sinDLat * sinDLat +\n        Math.cos((p1.lat * Math.PI) / 180) *\n            Math.cos((p2.lat * Math.PI) / 180) *\n            sinDLon *\n            sinDLon;\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n};\n/**\n * Converts a LatLng bound to pixels.\n *\n * @hidden\n */\nconst latLngBoundsToPixelBounds = (bounds, projection) => {\n    return {\n        northEast: projection.fromLatLngToDivPixel(bounds.getNorthEast()),\n        southWest: projection.fromLatLngToDivPixel(bounds.getSouthWest()),\n    };\n};\n/**\n * Extends a pixel bounds by numPixels in all directions.\n *\n * @hidden\n */\nconst extendPixelBounds = ({ northEast, southWest }, numPixels) => {\n    northEast.x += numPixels;\n    northEast.y -= numPixels;\n    southWest.x -= numPixels;\n    southWest.y += numPixels;\n    return { northEast, southWest };\n};\n/**\n * @hidden\n */\nconst pixelBoundsToLatLngBounds = ({ northEast, southWest }, projection) => {\n    const sw = projection.fromDivPixelToLatLng(southWest);\n    const ne = projection.fromDivPixelToLatLng(northEast);\n    return new google.maps.LatLngBounds(sw, ne);\n};\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @hidden\n */\nclass AbstractAlgorithm {\n    constructor({ maxZoom = 16 }) {\n        this.maxZoom = maxZoom;\n    }\n    /**\n     * Helper function to bypass clustering based upon some map state such as\n     * zoom, number of markers, etc.\n     *\n     * ```typescript\n     *  cluster({markers, map}: AlgorithmInput): Cluster[] {\n     *    if (shouldBypassClustering(map)) {\n     *      return this.noop({markers})\n     *    }\n     * }\n     * ```\n     */\n    noop({ markers, }) {\n        return noop(markers);\n    }\n}\n/**\n * Abstract viewport algorithm proves a class to filter markers by a padded\n * viewport. This is a common optimization.\n *\n * @hidden\n */\nclass AbstractViewportAlgorithm extends AbstractAlgorithm {\n    constructor(_a) {\n        var { viewportPadding = 60 } = _a, options = __rest(_a, [\"viewportPadding\"]);\n        super(options);\n        this.viewportPadding = 60;\n        this.viewportPadding = viewportPadding;\n    }\n    calculate({ markers, map, mapCanvasProjection, }) {\n        if (map.getZoom() >= this.maxZoom) {\n            return {\n                clusters: this.noop({\n                    markers,\n                }),\n                changed: false,\n            };\n        }\n        return {\n            clusters: this.cluster({\n                markers: filterMarkersToPaddedViewport(map, mapCanvasProjection, markers, this.viewportPadding),\n                map,\n                mapCanvasProjection,\n            }),\n        };\n    }\n}\n/**\n * @hidden\n */\nconst noop = (markers) => {\n    const clusters = markers.map((marker) => new Cluster({\n        position: MarkerUtils.getPosition(marker),\n        markers: [marker],\n    }));\n    return clusters;\n};\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The default Grid algorithm historically used in Google Maps marker\n * clustering.\n *\n * The Grid algorithm does not implement caching and markers may flash as the\n * viewport changes. Instead use {@link SuperClusterAlgorithm}.\n */\nclass GridAlgorithm extends AbstractViewportAlgorithm {\n    constructor(_a) {\n        var { maxDistance = 40000, gridSize = 40 } = _a, options = __rest(_a, [\"maxDistance\", \"gridSize\"]);\n        super(options);\n        this.clusters = [];\n        this.state = { zoom: -1 };\n        this.maxDistance = maxDistance;\n        this.gridSize = gridSize;\n    }\n    calculate({ markers, map, mapCanvasProjection, }) {\n        const state = { zoom: map.getZoom() };\n        let changed = false;\n        if (this.state.zoom >= this.maxZoom && state.zoom >= this.maxZoom) ;\n        else {\n            changed = !fast_deep_equal__WEBPACK_IMPORTED_MODULE_0___default()(this.state, state);\n        }\n        this.state = state;\n        if (map.getZoom() >= this.maxZoom) {\n            return {\n                clusters: this.noop({\n                    markers,\n                }),\n                changed,\n            };\n        }\n        return {\n            clusters: this.cluster({\n                markers: filterMarkersToPaddedViewport(map, mapCanvasProjection, markers, this.viewportPadding),\n                map,\n                mapCanvasProjection,\n            }),\n        };\n    }\n    cluster({ markers, map, mapCanvasProjection, }) {\n        this.clusters = [];\n        markers.forEach((marker) => {\n            this.addToClosestCluster(marker, map, mapCanvasProjection);\n        });\n        return this.clusters;\n    }\n    addToClosestCluster(marker, map, projection) {\n        let maxDistance = this.maxDistance; // Some large number\n        let cluster = null;\n        for (let i = 0; i < this.clusters.length; i++) {\n            const candidate = this.clusters[i];\n            const distance = distanceBetweenPoints(candidate.bounds.getCenter().toJSON(), MarkerUtils.getPosition(marker).toJSON());\n            if (distance < maxDistance) {\n                maxDistance = distance;\n                cluster = candidate;\n            }\n        }\n        if (cluster &&\n            extendBoundsToPaddedViewport(cluster.bounds, projection, this.gridSize).contains(MarkerUtils.getPosition(marker))) {\n            cluster.push(marker);\n        }\n        else {\n            const cluster = new Cluster({ markers: [marker] });\n            this.clusters.push(cluster);\n        }\n    }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Noop algorithm does not generate any clusters or filter markers by the an extended viewport.\n */\nclass NoopAlgorithm extends AbstractAlgorithm {\n    constructor(_a) {\n        var options = __rest(_a, []);\n        super(options);\n    }\n    calculate({ markers, map, mapCanvasProjection, }) {\n        return {\n            clusters: this.cluster({ markers, map, mapCanvasProjection }),\n            changed: false,\n        };\n    }\n    cluster(input) {\n        return this.noop(input);\n    }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * A very fast JavaScript algorithm for geospatial point clustering using KD trees.\n *\n * @see https://www.npmjs.com/package/supercluster for more information on options.\n */\nclass SuperClusterAlgorithm extends AbstractAlgorithm {\n    constructor(_a) {\n        var { maxZoom, radius = 60 } = _a, options = __rest(_a, [\"maxZoom\", \"radius\"]);\n        super({ maxZoom });\n        this.state = { zoom: -1 };\n        this.superCluster = new supercluster__WEBPACK_IMPORTED_MODULE_1__[\"default\"](Object.assign({ maxZoom: this.maxZoom, radius }, options));\n    }\n    calculate(input) {\n        let changed = false;\n        const state = { zoom: input.map.getZoom() };\n        if (!fast_deep_equal__WEBPACK_IMPORTED_MODULE_0___default()(input.markers, this.markers)) {\n            changed = true;\n            // TODO use proxy to avoid copy?\n            this.markers = [...input.markers];\n            const points = this.markers.map((marker) => {\n                const position = MarkerUtils.getPosition(marker);\n                const coordinates = [position.lng(), position.lat()];\n                return {\n                    type: \"Feature\",\n                    geometry: {\n                        type: \"Point\",\n                        coordinates,\n                    },\n                    properties: { marker },\n                };\n            });\n            this.superCluster.load(points);\n        }\n        if (!changed) {\n            if (this.state.zoom <= this.maxZoom || state.zoom <= this.maxZoom) {\n                changed = !fast_deep_equal__WEBPACK_IMPORTED_MODULE_0___default()(this.state, state);\n            }\n        }\n        this.state = state;\n        if (changed) {\n            this.clusters = this.cluster(input);\n        }\n        return { clusters: this.clusters, changed };\n    }\n    cluster({ map }) {\n        return this.superCluster\n            .getClusters([-180, -90, 180, 90], Math.round(map.getZoom()))\n            .map((feature) => this.transformCluster(feature));\n    }\n    transformCluster({ geometry: { coordinates: [lng, lat], }, properties, }) {\n        if (properties.cluster) {\n            return new Cluster({\n                markers: this.superCluster\n                    .getLeaves(properties.cluster_id, Infinity)\n                    .map((leaf) => leaf.properties.marker),\n                position: { lat, lng },\n            });\n        }\n        const marker = properties.marker;\n        return new Cluster({\n            markers: [marker],\n            position: MarkerUtils.getPosition(marker),\n        });\n    }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * A very fast JavaScript algorithm for geospatial point clustering using KD trees.\n *\n * @see https://www.npmjs.com/package/supercluster for more information on options.\n */\nclass SuperClusterViewportAlgorithm extends AbstractViewportAlgorithm {\n    constructor(_a) {\n        var { maxZoom, radius = 60, viewportPadding = 60 } = _a, options = __rest(_a, [\"maxZoom\", \"radius\", \"viewportPadding\"]);\n        super({ maxZoom, viewportPadding });\n        this.superCluster = new supercluster__WEBPACK_IMPORTED_MODULE_1__[\"default\"](Object.assign({ maxZoom: this.maxZoom, radius }, options));\n        this.state = { zoom: -1, view: [0, 0, 0, 0] };\n    }\n    calculate(input) {\n        const state = {\n            zoom: Math.round(input.map.getZoom()),\n            view: getPaddedViewport(input.map.getBounds(), input.mapCanvasProjection, this.viewportPadding),\n        };\n        let changed = !fast_deep_equal__WEBPACK_IMPORTED_MODULE_0___default()(this.state, state);\n        if (!fast_deep_equal__WEBPACK_IMPORTED_MODULE_0___default()(input.markers, this.markers)) {\n            changed = true;\n            // TODO use proxy to avoid copy?\n            this.markers = [...input.markers];\n            const points = this.markers.map((marker) => {\n                const position = MarkerUtils.getPosition(marker);\n                const coordinates = [position.lng(), position.lat()];\n                return {\n                    type: \"Feature\",\n                    geometry: {\n                        type: \"Point\",\n                        coordinates,\n                    },\n                    properties: { marker },\n                };\n            });\n            this.superCluster.load(points);\n        }\n        if (changed) {\n            this.clusters = this.cluster(input);\n            this.state = state;\n        }\n        return { clusters: this.clusters, changed };\n    }\n    cluster({ map, mapCanvasProjection }) {\n        /* recalculate new state because we can't use the cached version. */\n        const state = {\n            zoom: Math.round(map.getZoom()),\n            view: getPaddedViewport(map.getBounds(), mapCanvasProjection, this.viewportPadding),\n        };\n        return this.superCluster\n            .getClusters(state.view, state.zoom)\n            .map((feature) => this.transformCluster(feature));\n    }\n    transformCluster({ geometry: { coordinates: [lng, lat], }, properties, }) {\n        if (properties.cluster) {\n            return new Cluster({\n                markers: this.superCluster\n                    .getLeaves(properties.cluster_id, Infinity)\n                    .map((leaf) => leaf.properties.marker),\n                position: { lat, lng },\n            });\n        }\n        const marker = properties.marker;\n        return new Cluster({\n            markers: [marker],\n            position: MarkerUtils.getPosition(marker),\n        });\n    }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provides statistics on all clusters in the current render cycle for use in {@link Renderer.render}.\n */\nclass ClusterStats {\n    constructor(markers, clusters) {\n        this.markers = { sum: markers.length };\n        const clusterMarkerCounts = clusters.map((a) => a.count);\n        const clusterMarkerSum = clusterMarkerCounts.reduce((a, b) => a + b, 0);\n        this.clusters = {\n            count: clusters.length,\n            markers: {\n                mean: clusterMarkerSum / clusters.length,\n                sum: clusterMarkerSum,\n                min: Math.min(...clusterMarkerCounts),\n                max: Math.max(...clusterMarkerCounts),\n            },\n        };\n    }\n}\nclass DefaultRenderer {\n    /**\n     * The default render function for the library used by {@link MarkerClusterer}.\n     *\n     * Currently set to use the following:\n     *\n     * ```typescript\n     * // change color if this cluster has more markers than the mean cluster\n     * const color =\n     *   count > Math.max(10, stats.clusters.markers.mean)\n     *     ? \"#ff0000\"\n     *     : \"#0000ff\";\n     *\n     * // create svg url with fill color\n     * const svg = window.btoa(`\n     * <svg fill=\"${color}\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 240 240\">\n     *   <circle cx=\"120\" cy=\"120\" opacity=\".6\" r=\"70\" />\n     *   <circle cx=\"120\" cy=\"120\" opacity=\".3\" r=\"90\" />\n     *   <circle cx=\"120\" cy=\"120\" opacity=\".2\" r=\"110\" />\n     *   <circle cx=\"120\" cy=\"120\" opacity=\".1\" r=\"130\" />\n     * </svg>`);\n     *\n     * // create marker using svg icon\n     * return new google.maps.Marker({\n     *   position,\n     *   icon: {\n     *     url: `data:image/svg+xml;base64,${svg}`,\n     *     scaledSize: new google.maps.Size(45, 45),\n     *   },\n     *   label: {\n     *     text: String(count),\n     *     color: \"rgba(255,255,255,0.9)\",\n     *     fontSize: \"12px\",\n     *   },\n     *   // adjust zIndex to be above other markers\n     *   zIndex: 1000 + count,\n     * });\n     * ```\n     */\n    render({ count, position }, stats, map) {\n        // change color if this cluster has more markers than the mean cluster\n        const color = count > Math.max(10, stats.clusters.markers.mean) ? \"#ff0000\" : \"#0000ff\";\n        // create svg literal with fill color\n        const svg = `<svg fill=\"${color}\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 240 240\" width=\"50\" height=\"50\">\n<circle cx=\"120\" cy=\"120\" opacity=\".6\" r=\"70\" />\n<circle cx=\"120\" cy=\"120\" opacity=\".3\" r=\"90\" />\n<circle cx=\"120\" cy=\"120\" opacity=\".2\" r=\"110\" />\n<text x=\"50%\" y=\"50%\" style=\"fill:#fff\" text-anchor=\"middle\" font-size=\"50\" dominant-baseline=\"middle\" font-family=\"roboto,arial,sans-serif\">${count}</text>\n</svg>`;\n        const title = `Cluster of ${count} markers`, \n        // adjust zIndex to be above other markers\n        zIndex = Number(google.maps.Marker.MAX_ZINDEX) + count;\n        if (MarkerUtils.isAdvancedMarkerAvailable(map)) {\n            // create cluster SVG element\n            const parser = new DOMParser();\n            const svgEl = parser.parseFromString(svg, \"image/svg+xml\").documentElement;\n            svgEl.setAttribute(\"transform\", \"translate(0 25)\");\n            const clusterOptions = {\n                map,\n                position,\n                zIndex,\n                title,\n                content: svgEl,\n            };\n            return new google.maps.marker.AdvancedMarkerElement(clusterOptions);\n        }\n        const clusterOptions = {\n            position,\n            zIndex,\n            title,\n            icon: {\n                url: `data:image/svg+xml;base64,${btoa(svg)}`,\n                anchor: new google.maps.Point(25, 25),\n            },\n        };\n        return new google.maps.Marker(clusterOptions);\n    }\n}\n\n/**\n * Copyright 2019 Google LLC. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Extends an object's prototype by another's.\n *\n * @param type1 The Type to be extended.\n * @param type2 The Type to extend with.\n * @ignore\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction extend(type1, type2) {\n    /* istanbul ignore next */\n    // eslint-disable-next-line prefer-const\n    for (let property in type2.prototype) {\n        type1.prototype[property] = type2.prototype[property];\n    }\n}\n/**\n * @ignore\n */\nclass OverlayViewSafe {\n    constructor() {\n        // MarkerClusterer implements google.maps.OverlayView interface. We use the\n        // extend function to extend MarkerClusterer with google.maps.OverlayView\n        // because it might not always be available when the code is defined so we\n        // look for it at the last possible moment. If it doesn't exist now then\n        // there is no point going ahead :)\n        extend(OverlayViewSafe, google.maps.OverlayView);\n    }\n}\n\n/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar MarkerClustererEvents;\n(function (MarkerClustererEvents) {\n    MarkerClustererEvents[\"CLUSTERING_BEGIN\"] = \"clusteringbegin\";\n    MarkerClustererEvents[\"CLUSTERING_END\"] = \"clusteringend\";\n    MarkerClustererEvents[\"CLUSTER_CLICK\"] = \"click\";\n})(MarkerClustererEvents || (MarkerClustererEvents = {}));\nconst defaultOnClusterClickHandler = (_, cluster, map) => {\n    map.fitBounds(cluster.bounds);\n};\n/**\n * MarkerClusterer creates and manages per-zoom-level clusters for large amounts\n * of markers. See {@link MarkerClustererOptions} for more details.\n *\n */\nclass MarkerClusterer extends OverlayViewSafe {\n    constructor({ map, markers = [], algorithmOptions = {}, algorithm = new SuperClusterAlgorithm(algorithmOptions), renderer = new DefaultRenderer(), onClusterClick = defaultOnClusterClickHandler, }) {\n        super();\n        this.markers = [...markers];\n        this.clusters = [];\n        this.algorithm = algorithm;\n        this.renderer = renderer;\n        this.onClusterClick = onClusterClick;\n        if (map) {\n            this.setMap(map);\n        }\n    }\n    addMarker(marker, noDraw) {\n        if (this.markers.includes(marker)) {\n            return;\n        }\n        this.markers.push(marker);\n        if (!noDraw) {\n            this.render();\n        }\n    }\n    addMarkers(markers, noDraw) {\n        markers.forEach((marker) => {\n            this.addMarker(marker, true);\n        });\n        if (!noDraw) {\n            this.render();\n        }\n    }\n    removeMarker(marker, noDraw) {\n        const index = this.markers.indexOf(marker);\n        if (index === -1) {\n            // Marker is not in our list of markers, so do nothing:\n            return false;\n        }\n        MarkerUtils.setMap(marker, null);\n        this.markers.splice(index, 1); // Remove the marker from the list of managed markers\n        if (!noDraw) {\n            this.render();\n        }\n        return true;\n    }\n    removeMarkers(markers, noDraw) {\n        let removed = false;\n        markers.forEach((marker) => {\n            removed = this.removeMarker(marker, true) || removed;\n        });\n        if (removed && !noDraw) {\n            this.render();\n        }\n        return removed;\n    }\n    clearMarkers(noDraw) {\n        this.markers.length = 0;\n        if (!noDraw) {\n            this.render();\n        }\n    }\n    /**\n     * Recalculates and draws all the marker clusters.\n     */\n    render() {\n        const map = this.getMap();\n        if (map instanceof google.maps.Map && map.getProjection()) {\n            google.maps.event.trigger(this, MarkerClustererEvents.CLUSTERING_BEGIN, this);\n            const { clusters, changed } = this.algorithm.calculate({\n                markers: this.markers,\n                map,\n                mapCanvasProjection: this.getProjection(),\n            });\n            // Allow algorithms to return flag on whether the clusters/markers have changed.\n            if (changed || changed == undefined) {\n                // Accumulate the markers of the clusters composed of a single marker.\n                // Those clusters directly use the marker.\n                // Clusters with more than one markers use a group marker generated by a renderer.\n                const singleMarker = new Set();\n                for (const cluster of clusters) {\n                    if (cluster.markers.length == 1) {\n                        singleMarker.add(cluster.markers[0]);\n                    }\n                }\n                const groupMarkers = [];\n                // Iterate the clusters that are currently rendered.\n                for (const cluster of this.clusters) {\n                    if (cluster.marker == null) {\n                        continue;\n                    }\n                    if (cluster.markers.length == 1) {\n                        if (!singleMarker.has(cluster.marker)) {\n                            // The marker:\n                            // - was previously rendered because it is from a cluster with 1 marker,\n                            // - should no more be rendered as it is not in singleMarker.\n                            MarkerUtils.setMap(cluster.marker, null);\n                        }\n                    }\n                    else {\n                        // Delay the removal of old group markers to avoid flickering.\n                        groupMarkers.push(cluster.marker);\n                    }\n                }\n                this.clusters = clusters;\n                this.renderClusters();\n                // Delayed removal of the markers of the former groups.\n                requestAnimationFrame(() => groupMarkers.forEach((marker) => MarkerUtils.setMap(marker, null)));\n            }\n            google.maps.event.trigger(this, MarkerClustererEvents.CLUSTERING_END, this);\n        }\n    }\n    onAdd() {\n        this.idleListener = this.getMap().addListener(\"idle\", this.render.bind(this));\n        this.render();\n    }\n    onRemove() {\n        google.maps.event.removeListener(this.idleListener);\n        this.reset();\n    }\n    reset() {\n        this.markers.forEach((marker) => MarkerUtils.setMap(marker, null));\n        this.clusters.forEach((cluster) => cluster.delete());\n        this.clusters = [];\n    }\n    renderClusters() {\n        // Generate stats to pass to renderers.\n        const stats = new ClusterStats(this.markers, this.clusters);\n        const map = this.getMap();\n        this.clusters.forEach((cluster) => {\n            if (cluster.markers.length === 1) {\n                cluster.marker = cluster.markers[0];\n            }\n            else {\n                // Generate the marker to represent the group.\n                cluster.marker = this.renderer.render(cluster, stats, map);\n                // Make sure all individual markers are removed from the map.\n                cluster.markers.forEach((marker) => MarkerUtils.setMap(marker, null));\n                if (this.onClusterClick) {\n                    cluster.marker.addListener(\"click\", \n                    /* istanbul ignore next */\n                    (event) => {\n                        google.maps.event.trigger(this, MarkerClustererEvents.CLUSTER_CLICK, cluster);\n                        this.onClusterClick(event, cluster, map);\n                    });\n                }\n            }\n            MarkerUtils.setMap(cluster.marker, map);\n        });\n    }\n}\n\n\n//# sourceMappingURL=index.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGdvb2dsZW1hcHMvbWFya2VyY2x1c3RlcmVyL2Rpc3QvaW5kZXguZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQW9DO0FBQ0k7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2REFBNkQsY0FBYztBQUMzRTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixtQkFBbUI7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksdUJBQXVCO0FBQ25DLG9EQUFvRCxzQkFBc0I7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixzQkFBc0I7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsc0JBQXNCO0FBQzNEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGNBQWM7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsYUFBYTtBQUM5QjtBQUNBLDhCQUE4QixRQUFRO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxVQUFVO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLHVCQUF1QjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixvQ0FBb0M7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsNEJBQTRCO0FBQzlEO0FBQ0E7QUFDQTtBQUNBLGNBQWMscUNBQXFDO0FBQ25EO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLG9DQUFvQztBQUNwRCx3QkFBd0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHNEQUFLO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxjQUFjLG9DQUFvQztBQUNsRDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsNENBQTRDO0FBQzVDO0FBQ0Esd0JBQXdCLDBCQUEwQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQ0FBMEMsbUJBQW1CO0FBQzdEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0Isb0NBQW9DO0FBQ3BEO0FBQ0EscUNBQXFDLG1DQUFtQztBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsdUJBQXVCO0FBQ3JDLGdCQUFnQixTQUFTO0FBQ3pCLHVCQUF1QjtBQUN2QixnQ0FBZ0Msb0RBQVksaUJBQWlCLCtCQUErQjtBQUM1RjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEIsYUFBYSxzREFBSztBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLGtDQUFrQyxRQUFRO0FBQzFDO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHNEQUFLO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLGNBQWMsS0FBSztBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixZQUFZLDBCQUEwQixlQUFlO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsVUFBVTtBQUN0QyxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyw2Q0FBNkM7QUFDM0QsZ0JBQWdCLDBCQUEwQjtBQUMxQyxnQ0FBZ0Msb0RBQVksaUJBQWlCLCtCQUErQjtBQUM1Rix1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHNEQUFLO0FBQzVCLGFBQWEsc0RBQUs7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQixrQ0FBa0MsUUFBUTtBQUMxQztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxjQUFjLDBCQUEwQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsWUFBWSwwQkFBMEIsZUFBZTtBQUM1RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLFVBQVU7QUFDdEMsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtFQUErRSxzQkFBc0I7QUFDckc7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNERBQTRELHNCQUFzQjtBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsTUFBTTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxTQUFTLElBQUk7QUFDakQ7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsYUFBYSxpQkFBaUI7QUFDOUI7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLE1BQU07QUFDeEM7QUFDQTtBQUNBO0FBQ0EsK0lBQStJLE1BQU07QUFDcko7QUFDQSxvQ0FBb0MsT0FBTztBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUMsU0FBUyxVQUFVO0FBQzVEO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNEQUFzRDtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDhCQUE4QjtBQUNsRDtBQUNBO0FBQ0E7QUFDQSxrQkFBa0Isd0NBQXdDLDZJQUE2STtBQUN2TTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isb0JBQW9CO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTs7QUFFa2E7QUFDbGEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9AZ29vZ2xlbWFwcy9tYXJrZXJjbHVzdGVyZXIvZGlzdC9pbmRleC5lc20uanM/ZjNjMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZXF1YWwgZnJvbSAnZmFzdC1kZWVwLWVxdWFsJztcbmltcG9ydCBTdXBlckNsdXN0ZXIgZnJvbSAnc3VwZXJjbHVzdGVyJztcblxuLyohICoqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqXHJcbkNvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLlxyXG5cclxuUGVybWlzc2lvbiB0byB1c2UsIGNvcHksIG1vZGlmeSwgYW5kL29yIGRpc3RyaWJ1dGUgdGhpcyBzb2Z0d2FyZSBmb3IgYW55XHJcbnB1cnBvc2Ugd2l0aCBvciB3aXRob3V0IGZlZSBpcyBoZXJlYnkgZ3JhbnRlZC5cclxuXHJcblRIRSBTT0ZUV0FSRSBJUyBQUk9WSURFRCBcIkFTIElTXCIgQU5EIFRIRSBBVVRIT1IgRElTQ0xBSU1TIEFMTCBXQVJSQU5USUVTIFdJVEhcclxuUkVHQVJEIFRPIFRISVMgU09GVFdBUkUgSU5DTFVESU5HIEFMTCBJTVBMSUVEIFdBUlJBTlRJRVMgT0YgTUVSQ0hBTlRBQklMSVRZXHJcbkFORCBGSVRORVNTLiBJTiBOTyBFVkVOVCBTSEFMTCBUSEUgQVVUSE9SIEJFIExJQUJMRSBGT1IgQU5ZIFNQRUNJQUwsIERJUkVDVCxcclxuSU5ESVJFQ1QsIE9SIENPTlNFUVVFTlRJQUwgREFNQUdFUyBPUiBBTlkgREFNQUdFUyBXSEFUU09FVkVSIFJFU1VMVElORyBGUk9NXHJcbkxPU1MgT0YgVVNFLCBEQVRBIE9SIFBST0ZJVFMsIFdIRVRIRVIgSU4gQU4gQUNUSU9OIE9GIENPTlRSQUNULCBORUdMSUdFTkNFIE9SXHJcbk9USEVSIFRPUlRJT1VTIEFDVElPTiwgQVJJU0lORyBPVVQgT0YgT1IgSU4gQ09OTkVDVElPTiBXSVRIIFRIRSBVU0UgT1JcclxuUEVSRk9STUFOQ0UgT0YgVEhJUyBTT0ZUV0FSRS5cclxuKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKiogKi9cclxuXHJcbmZ1bmN0aW9uIF9fcmVzdChzLCBlKSB7XHJcbiAgICB2YXIgdCA9IHt9O1xyXG4gICAgZm9yICh2YXIgcCBpbiBzKSBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHMsIHApICYmIGUuaW5kZXhPZihwKSA8IDApXHJcbiAgICAgICAgdFtwXSA9IHNbcF07XHJcbiAgICBpZiAocyAhPSBudWxsICYmIHR5cGVvZiBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzID09PSBcImZ1bmN0aW9uXCIpXHJcbiAgICAgICAgZm9yICh2YXIgaSA9IDAsIHAgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKHMpOyBpIDwgcC5sZW5ndGg7IGkrKykge1xyXG4gICAgICAgICAgICBpZiAoZS5pbmRleE9mKHBbaV0pIDwgMCAmJiBPYmplY3QucHJvdG90eXBlLnByb3BlcnR5SXNFbnVtZXJhYmxlLmNhbGwocywgcFtpXSkpXHJcbiAgICAgICAgICAgICAgICB0W3BbaV1dID0gc1twW2ldXTtcclxuICAgICAgICB9XHJcbiAgICByZXR1cm4gdDtcclxufVxuXG4vKipcbiAqIENvcHlyaWdodCAyMDIzIEdvb2dsZSBMTENcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vKipcbiAqIHV0aWwgY2xhc3MgdGhhdCBjcmVhdGVzIGEgY29tbW9uIHNldCBvZiBjb252ZW5pZW5jZSBmdW5jdGlvbnMgdG8gd3JhcFxuICogc2hhcmVkIGJlaGF2aW9yIG9mIEFkdmFuY2VkIE1hcmtlcnMgYW5kIE1hcmtlcnMuXG4gKi9cbmNsYXNzIE1hcmtlclV0aWxzIHtcbiAgICBzdGF0aWMgaXNBZHZhbmNlZE1hcmtlckF2YWlsYWJsZShtYXApIHtcbiAgICAgICAgcmV0dXJuIChnb29nbGUubWFwcy5tYXJrZXIgJiZcbiAgICAgICAgICAgIG1hcC5nZXRNYXBDYXBhYmlsaXRpZXMoKS5pc0FkdmFuY2VkTWFya2Vyc0F2YWlsYWJsZSA9PT0gdHJ1ZSk7XG4gICAgfVxuICAgIHN0YXRpYyBpc0FkdmFuY2VkTWFya2VyKG1hcmtlcikge1xuICAgICAgICByZXR1cm4gKGdvb2dsZS5tYXBzLm1hcmtlciAmJlxuICAgICAgICAgICAgbWFya2VyIGluc3RhbmNlb2YgZ29vZ2xlLm1hcHMubWFya2VyLkFkdmFuY2VkTWFya2VyRWxlbWVudCk7XG4gICAgfVxuICAgIHN0YXRpYyBzZXRNYXAobWFya2VyLCBtYXApIHtcbiAgICAgICAgaWYgKHRoaXMuaXNBZHZhbmNlZE1hcmtlcihtYXJrZXIpKSB7XG4gICAgICAgICAgICBtYXJrZXIubWFwID0gbWFwO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgbWFya2VyLnNldE1hcChtYXApO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRpYyBnZXRQb3NpdGlvbihtYXJrZXIpIHtcbiAgICAgICAgLy8gU3VwZXJDbHVzdGVyQWxnb3JpdGhtLmNhbGN1bGF0ZSBleHBlY3RzIGEgTGF0TG5nIGluc3RhbmNlIHNvIHdlIGZha2UgaXQgZm9yIEFkdiBNYXJrZXJzXG4gICAgICAgIGlmICh0aGlzLmlzQWR2YW5jZWRNYXJrZXIobWFya2VyKSkge1xuICAgICAgICAgICAgaWYgKG1hcmtlci5wb3NpdGlvbikge1xuICAgICAgICAgICAgICAgIGlmIChtYXJrZXIucG9zaXRpb24gaW5zdGFuY2VvZiBnb29nbGUubWFwcy5MYXRMbmcpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG1hcmtlci5wb3NpdGlvbjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gc2luY2Ugd2UgY2FuJ3QgY2FzdCB0byBMYXRMbmdMaXRlcmFsIGZvciByZWFzb25zID0oXG4gICAgICAgICAgICAgICAgaWYgKG1hcmtlci5wb3NpdGlvbi5sYXQgJiYgbWFya2VyLnBvc2l0aW9uLmxuZykge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbmV3IGdvb2dsZS5tYXBzLkxhdExuZyhtYXJrZXIucG9zaXRpb24ubGF0LCBtYXJrZXIucG9zaXRpb24ubG5nKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gbmV3IGdvb2dsZS5tYXBzLkxhdExuZyhudWxsKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbWFya2VyLmdldFBvc2l0aW9uKCk7XG4gICAgfVxuICAgIHN0YXRpYyBnZXRWaXNpYmxlKG1hcmtlcikge1xuICAgICAgICBpZiAodGhpcy5pc0FkdmFuY2VkTWFya2VyKG1hcmtlcikpIHtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogQWx3YXlzIHJldHVybiB0cnVlIGZvciBBZHZhbmNlZCBNYXJrZXJzIGJlY2F1c2UgdGhlIGNsdXN0ZXJlclxuICAgICAgICAgICAgICogdXNlcyBnZXRWaXNpYmxlIGFzIGEgd2F5IHRvIGNvdW50IGxlZ2FjeSBtYXJrZXJzIG5vdCBhcyBhbiBhY3R1YWxcbiAgICAgICAgICAgICAqIGluZGljYXRvciBvZiB2aXNpYmlsaXR5IGZvciBzb21lIHJlYXNvbi4gRXZlbiB3aGVuIG1hcmtlcnMgYXJlIGhpZGRlblxuICAgICAgICAgICAgICogTWFya2VyLmdldFZpc2libGUgcmV0dXJucyBgdHJ1ZWAgYW5kIHRoaXMgaXMgdXNlZCB0byBzZXQgdGhlIG1hcmtlciBjb3VudFxuICAgICAgICAgICAgICogb24gdGhlIGNsdXN0ZXIuIFNlZSB0aGUgYmVoYXZpb3Igb2YgQ2x1c3Rlci5jb3VudFxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbWFya2VyLmdldFZpc2libGUoKTtcbiAgICB9XG59XG5cbi8qKlxuICogQ29weXJpZ2h0IDIwMjEgR29vZ2xlIExMQ1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbmNsYXNzIENsdXN0ZXIge1xuICAgIGNvbnN0cnVjdG9yKHsgbWFya2VycywgcG9zaXRpb24gfSkge1xuICAgICAgICB0aGlzLm1hcmtlcnMgPSBtYXJrZXJzO1xuICAgICAgICBpZiAocG9zaXRpb24pIHtcbiAgICAgICAgICAgIGlmIChwb3NpdGlvbiBpbnN0YW5jZW9mIGdvb2dsZS5tYXBzLkxhdExuZykge1xuICAgICAgICAgICAgICAgIHRoaXMuX3Bvc2l0aW9uID0gcG9zaXRpb247XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9wb3NpdGlvbiA9IG5ldyBnb29nbGUubWFwcy5MYXRMbmcocG9zaXRpb24pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGdldCBib3VuZHMoKSB7XG4gICAgICAgIGlmICh0aGlzLm1hcmtlcnMubGVuZ3RoID09PSAwICYmICF0aGlzLl9wb3NpdGlvbikge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGJvdW5kcyA9IG5ldyBnb29nbGUubWFwcy5MYXRMbmdCb3VuZHModGhpcy5fcG9zaXRpb24sIHRoaXMuX3Bvc2l0aW9uKTtcbiAgICAgICAgZm9yIChjb25zdCBtYXJrZXIgb2YgdGhpcy5tYXJrZXJzKSB7XG4gICAgICAgICAgICBib3VuZHMuZXh0ZW5kKE1hcmtlclV0aWxzLmdldFBvc2l0aW9uKG1hcmtlcikpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBib3VuZHM7XG4gICAgfVxuICAgIGdldCBwb3NpdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3Bvc2l0aW9uIHx8IHRoaXMuYm91bmRzLmdldENlbnRlcigpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgdGhlIGNvdW50IG9mICoqdmlzaWJsZSoqIG1hcmtlcnMuXG4gICAgICovXG4gICAgZ2V0IGNvdW50KCkge1xuICAgICAgICByZXR1cm4gdGhpcy5tYXJrZXJzLmZpbHRlcigobSkgPT4gTWFya2VyVXRpbHMuZ2V0VmlzaWJsZShtKSkubGVuZ3RoO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBBZGQgYSBtYXJrZXIgdG8gdGhlIGNsdXN0ZXIuXG4gICAgICovXG4gICAgcHVzaChtYXJrZXIpIHtcbiAgICAgICAgdGhpcy5tYXJrZXJzLnB1c2gobWFya2VyKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2xlYW51cCByZWZlcmVuY2VzIGFuZCByZW1vdmUgbWFya2VyIGZyb20gbWFwLlxuICAgICAqL1xuICAgIGRlbGV0ZSgpIHtcbiAgICAgICAgaWYgKHRoaXMubWFya2VyKSB7XG4gICAgICAgICAgICBNYXJrZXJVdGlscy5zZXRNYXAodGhpcy5tYXJrZXIsIG51bGwpO1xuICAgICAgICAgICAgdGhpcy5tYXJrZXIgPSB1bmRlZmluZWQ7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5tYXJrZXJzLmxlbmd0aCA9IDA7XG4gICAgfVxufVxuXG4vKipcbiAqIENvcHlyaWdodCAyMDIxIEdvb2dsZSBMTENcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vKipcbiAqIFJldHVybnMgdGhlIG1hcmtlcnMgdmlzaWJsZSBpbiBhIHBhZGRlZCBtYXAgdmlld3BvcnRcbiAqXG4gKiBAcGFyYW0gbWFwXG4gKiBAcGFyYW0gbWFwQ2FudmFzUHJvamVjdGlvblxuICogQHBhcmFtIG1hcmtlcnMgVGhlIGxpc3Qgb2YgbWFya2VyIHRvIGZpbHRlclxuICogQHBhcmFtIHZpZXdwb3J0UGFkZGluZ1BpeGVscyBUaGUgcGFkZGluZyBpbiBwaXhlbFxuICogQHJldHVybnMgVGhlIGxpc3Qgb2YgbWFya2VycyBpbiB0aGUgcGFkZGVkIHZpZXdwb3J0XG4gKi9cbmNvbnN0IGZpbHRlck1hcmtlcnNUb1BhZGRlZFZpZXdwb3J0ID0gKG1hcCwgbWFwQ2FudmFzUHJvamVjdGlvbiwgbWFya2Vycywgdmlld3BvcnRQYWRkaW5nUGl4ZWxzKSA9PiB7XG4gICAgY29uc3QgZXh0ZW5kZWRNYXBCb3VuZHMgPSBleHRlbmRCb3VuZHNUb1BhZGRlZFZpZXdwb3J0KG1hcC5nZXRCb3VuZHMoKSwgbWFwQ2FudmFzUHJvamVjdGlvbiwgdmlld3BvcnRQYWRkaW5nUGl4ZWxzKTtcbiAgICByZXR1cm4gbWFya2Vycy5maWx0ZXIoKG1hcmtlcikgPT4gZXh0ZW5kZWRNYXBCb3VuZHMuY29udGFpbnMoTWFya2VyVXRpbHMuZ2V0UG9zaXRpb24obWFya2VyKSkpO1xufTtcbi8qKlxuICogRXh0ZW5kcyBhIGJvdW5kcyBieSBhIG51bWJlciBvZiBwaXhlbHMgaW4gZWFjaCBkaXJlY3Rpb25cbiAqL1xuY29uc3QgZXh0ZW5kQm91bmRzVG9QYWRkZWRWaWV3cG9ydCA9IChib3VuZHMsIHByb2plY3Rpb24sIG51bVBpeGVscykgPT4ge1xuICAgIGNvbnN0IHsgbm9ydGhFYXN0LCBzb3V0aFdlc3QgfSA9IGxhdExuZ0JvdW5kc1RvUGl4ZWxCb3VuZHMoYm91bmRzLCBwcm9qZWN0aW9uKTtcbiAgICBjb25zdCBleHRlbmRlZFBpeGVsQm91bmRzID0gZXh0ZW5kUGl4ZWxCb3VuZHMoeyBub3J0aEVhc3QsIHNvdXRoV2VzdCB9LCBudW1QaXhlbHMpO1xuICAgIHJldHVybiBwaXhlbEJvdW5kc1RvTGF0TG5nQm91bmRzKGV4dGVuZGVkUGl4ZWxCb3VuZHMsIHByb2plY3Rpb24pO1xufTtcbi8qKlxuICogR2V0cyB0aGUgZXh0ZW5kZWQgYm91bmRzIGFzIGEgYmJveCBbd2VzdExuZywgc291dGhMYXQsIGVhc3RMbmcsIG5vcnRoTGF0XVxuICovXG5jb25zdCBnZXRQYWRkZWRWaWV3cG9ydCA9IChib3VuZHMsIHByb2plY3Rpb24sIHBpeGVscykgPT4ge1xuICAgIGNvbnN0IGV4dGVuZGVkID0gZXh0ZW5kQm91bmRzVG9QYWRkZWRWaWV3cG9ydChib3VuZHMsIHByb2plY3Rpb24sIHBpeGVscyk7XG4gICAgY29uc3QgbmUgPSBleHRlbmRlZC5nZXROb3J0aEVhc3QoKTtcbiAgICBjb25zdCBzdyA9IGV4dGVuZGVkLmdldFNvdXRoV2VzdCgpO1xuICAgIHJldHVybiBbc3cubG5nKCksIHN3LmxhdCgpLCBuZS5sbmcoKSwgbmUubGF0KCldO1xufTtcbi8qKlxuICogUmV0dXJucyB0aGUgZGlzdGFuY2UgYmV0d2VlbiAyIHBvc2l0aW9ucy5cbiAqXG4gKiBAaGlkZGVuXG4gKi9cbmNvbnN0IGRpc3RhbmNlQmV0d2VlblBvaW50cyA9IChwMSwgcDIpID0+IHtcbiAgICBjb25zdCBSID0gNjM3MTsgLy8gUmFkaXVzIG9mIHRoZSBFYXJ0aCBpbiBrbVxuICAgIGNvbnN0IGRMYXQgPSAoKHAyLmxhdCAtIHAxLmxhdCkgKiBNYXRoLlBJKSAvIDE4MDtcbiAgICBjb25zdCBkTG9uID0gKChwMi5sbmcgLSBwMS5sbmcpICogTWF0aC5QSSkgLyAxODA7XG4gICAgY29uc3Qgc2luRExhdCA9IE1hdGguc2luKGRMYXQgLyAyKTtcbiAgICBjb25zdCBzaW5ETG9uID0gTWF0aC5zaW4oZExvbiAvIDIpO1xuICAgIGNvbnN0IGEgPSBzaW5ETGF0ICogc2luRExhdCArXG4gICAgICAgIE1hdGguY29zKChwMS5sYXQgKiBNYXRoLlBJKSAvIDE4MCkgKlxuICAgICAgICAgICAgTWF0aC5jb3MoKHAyLmxhdCAqIE1hdGguUEkpIC8gMTgwKSAqXG4gICAgICAgICAgICBzaW5ETG9uICpcbiAgICAgICAgICAgIHNpbkRMb247XG4gICAgY29uc3QgYyA9IDIgKiBNYXRoLmF0YW4yKE1hdGguc3FydChhKSwgTWF0aC5zcXJ0KDEgLSBhKSk7XG4gICAgcmV0dXJuIFIgKiBjO1xufTtcbi8qKlxuICogQ29udmVydHMgYSBMYXRMbmcgYm91bmQgdG8gcGl4ZWxzLlxuICpcbiAqIEBoaWRkZW5cbiAqL1xuY29uc3QgbGF0TG5nQm91bmRzVG9QaXhlbEJvdW5kcyA9IChib3VuZHMsIHByb2plY3Rpb24pID0+IHtcbiAgICByZXR1cm4ge1xuICAgICAgICBub3J0aEVhc3Q6IHByb2plY3Rpb24uZnJvbUxhdExuZ1RvRGl2UGl4ZWwoYm91bmRzLmdldE5vcnRoRWFzdCgpKSxcbiAgICAgICAgc291dGhXZXN0OiBwcm9qZWN0aW9uLmZyb21MYXRMbmdUb0RpdlBpeGVsKGJvdW5kcy5nZXRTb3V0aFdlc3QoKSksXG4gICAgfTtcbn07XG4vKipcbiAqIEV4dGVuZHMgYSBwaXhlbCBib3VuZHMgYnkgbnVtUGl4ZWxzIGluIGFsbCBkaXJlY3Rpb25zLlxuICpcbiAqIEBoaWRkZW5cbiAqL1xuY29uc3QgZXh0ZW5kUGl4ZWxCb3VuZHMgPSAoeyBub3J0aEVhc3QsIHNvdXRoV2VzdCB9LCBudW1QaXhlbHMpID0+IHtcbiAgICBub3J0aEVhc3QueCArPSBudW1QaXhlbHM7XG4gICAgbm9ydGhFYXN0LnkgLT0gbnVtUGl4ZWxzO1xuICAgIHNvdXRoV2VzdC54IC09IG51bVBpeGVscztcbiAgICBzb3V0aFdlc3QueSArPSBudW1QaXhlbHM7XG4gICAgcmV0dXJuIHsgbm9ydGhFYXN0LCBzb3V0aFdlc3QgfTtcbn07XG4vKipcbiAqIEBoaWRkZW5cbiAqL1xuY29uc3QgcGl4ZWxCb3VuZHNUb0xhdExuZ0JvdW5kcyA9ICh7IG5vcnRoRWFzdCwgc291dGhXZXN0IH0sIHByb2plY3Rpb24pID0+IHtcbiAgICBjb25zdCBzdyA9IHByb2plY3Rpb24uZnJvbURpdlBpeGVsVG9MYXRMbmcoc291dGhXZXN0KTtcbiAgICBjb25zdCBuZSA9IHByb2plY3Rpb24uZnJvbURpdlBpeGVsVG9MYXRMbmcobm9ydGhFYXN0KTtcbiAgICByZXR1cm4gbmV3IGdvb2dsZS5tYXBzLkxhdExuZ0JvdW5kcyhzdywgbmUpO1xufTtcblxuLyoqXG4gKiBDb3B5cmlnaHQgMjAyMSBHb29nbGUgTExDXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuLyoqXG4gKiBAaGlkZGVuXG4gKi9cbmNsYXNzIEFic3RyYWN0QWxnb3JpdGhtIHtcbiAgICBjb25zdHJ1Y3Rvcih7IG1heFpvb20gPSAxNiB9KSB7XG4gICAgICAgIHRoaXMubWF4Wm9vbSA9IG1heFpvb207XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEhlbHBlciBmdW5jdGlvbiB0byBieXBhc3MgY2x1c3RlcmluZyBiYXNlZCB1cG9uIHNvbWUgbWFwIHN0YXRlIHN1Y2ggYXNcbiAgICAgKiB6b29tLCBudW1iZXIgb2YgbWFya2VycywgZXRjLlxuICAgICAqXG4gICAgICogYGBgdHlwZXNjcmlwdFxuICAgICAqICBjbHVzdGVyKHttYXJrZXJzLCBtYXB9OiBBbGdvcml0aG1JbnB1dCk6IENsdXN0ZXJbXSB7XG4gICAgICogICAgaWYgKHNob3VsZEJ5cGFzc0NsdXN0ZXJpbmcobWFwKSkge1xuICAgICAqICAgICAgcmV0dXJuIHRoaXMubm9vcCh7bWFya2Vyc30pXG4gICAgICogICAgfVxuICAgICAqIH1cbiAgICAgKiBgYGBcbiAgICAgKi9cbiAgICBub29wKHsgbWFya2VycywgfSkge1xuICAgICAgICByZXR1cm4gbm9vcChtYXJrZXJzKTtcbiAgICB9XG59XG4vKipcbiAqIEFic3RyYWN0IHZpZXdwb3J0IGFsZ29yaXRobSBwcm92ZXMgYSBjbGFzcyB0byBmaWx0ZXIgbWFya2VycyBieSBhIHBhZGRlZFxuICogdmlld3BvcnQuIFRoaXMgaXMgYSBjb21tb24gb3B0aW1pemF0aW9uLlxuICpcbiAqIEBoaWRkZW5cbiAqL1xuY2xhc3MgQWJzdHJhY3RWaWV3cG9ydEFsZ29yaXRobSBleHRlbmRzIEFic3RyYWN0QWxnb3JpdGhtIHtcbiAgICBjb25zdHJ1Y3RvcihfYSkge1xuICAgICAgICB2YXIgeyB2aWV3cG9ydFBhZGRpbmcgPSA2MCB9ID0gX2EsIG9wdGlvbnMgPSBfX3Jlc3QoX2EsIFtcInZpZXdwb3J0UGFkZGluZ1wiXSk7XG4gICAgICAgIHN1cGVyKG9wdGlvbnMpO1xuICAgICAgICB0aGlzLnZpZXdwb3J0UGFkZGluZyA9IDYwO1xuICAgICAgICB0aGlzLnZpZXdwb3J0UGFkZGluZyA9IHZpZXdwb3J0UGFkZGluZztcbiAgICB9XG4gICAgY2FsY3VsYXRlKHsgbWFya2VycywgbWFwLCBtYXBDYW52YXNQcm9qZWN0aW9uLCB9KSB7XG4gICAgICAgIGlmIChtYXAuZ2V0Wm9vbSgpID49IHRoaXMubWF4Wm9vbSkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBjbHVzdGVyczogdGhpcy5ub29wKHtcbiAgICAgICAgICAgICAgICAgICAgbWFya2VycyxcbiAgICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgICBjaGFuZ2VkOiBmYWxzZSxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGNsdXN0ZXJzOiB0aGlzLmNsdXN0ZXIoe1xuICAgICAgICAgICAgICAgIG1hcmtlcnM6IGZpbHRlck1hcmtlcnNUb1BhZGRlZFZpZXdwb3J0KG1hcCwgbWFwQ2FudmFzUHJvamVjdGlvbiwgbWFya2VycywgdGhpcy52aWV3cG9ydFBhZGRpbmcpLFxuICAgICAgICAgICAgICAgIG1hcCxcbiAgICAgICAgICAgICAgICBtYXBDYW52YXNQcm9qZWN0aW9uLFxuICAgICAgICAgICAgfSksXG4gICAgICAgIH07XG4gICAgfVxufVxuLyoqXG4gKiBAaGlkZGVuXG4gKi9cbmNvbnN0IG5vb3AgPSAobWFya2VycykgPT4ge1xuICAgIGNvbnN0IGNsdXN0ZXJzID0gbWFya2Vycy5tYXAoKG1hcmtlcikgPT4gbmV3IENsdXN0ZXIoe1xuICAgICAgICBwb3NpdGlvbjogTWFya2VyVXRpbHMuZ2V0UG9zaXRpb24obWFya2VyKSxcbiAgICAgICAgbWFya2VyczogW21hcmtlcl0sXG4gICAgfSkpO1xuICAgIHJldHVybiBjbHVzdGVycztcbn07XG5cbi8qKlxuICogQ29weXJpZ2h0IDIwMjEgR29vZ2xlIExMQ1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8qKlxuICogVGhlIGRlZmF1bHQgR3JpZCBhbGdvcml0aG0gaGlzdG9yaWNhbGx5IHVzZWQgaW4gR29vZ2xlIE1hcHMgbWFya2VyXG4gKiBjbHVzdGVyaW5nLlxuICpcbiAqIFRoZSBHcmlkIGFsZ29yaXRobSBkb2VzIG5vdCBpbXBsZW1lbnQgY2FjaGluZyBhbmQgbWFya2VycyBtYXkgZmxhc2ggYXMgdGhlXG4gKiB2aWV3cG9ydCBjaGFuZ2VzLiBJbnN0ZWFkIHVzZSB7QGxpbmsgU3VwZXJDbHVzdGVyQWxnb3JpdGhtfS5cbiAqL1xuY2xhc3MgR3JpZEFsZ29yaXRobSBleHRlbmRzIEFic3RyYWN0Vmlld3BvcnRBbGdvcml0aG0ge1xuICAgIGNvbnN0cnVjdG9yKF9hKSB7XG4gICAgICAgIHZhciB7IG1heERpc3RhbmNlID0gNDAwMDAsIGdyaWRTaXplID0gNDAgfSA9IF9hLCBvcHRpb25zID0gX19yZXN0KF9hLCBbXCJtYXhEaXN0YW5jZVwiLCBcImdyaWRTaXplXCJdKTtcbiAgICAgICAgc3VwZXIob3B0aW9ucyk7XG4gICAgICAgIHRoaXMuY2x1c3RlcnMgPSBbXTtcbiAgICAgICAgdGhpcy5zdGF0ZSA9IHsgem9vbTogLTEgfTtcbiAgICAgICAgdGhpcy5tYXhEaXN0YW5jZSA9IG1heERpc3RhbmNlO1xuICAgICAgICB0aGlzLmdyaWRTaXplID0gZ3JpZFNpemU7XG4gICAgfVxuICAgIGNhbGN1bGF0ZSh7IG1hcmtlcnMsIG1hcCwgbWFwQ2FudmFzUHJvamVjdGlvbiwgfSkge1xuICAgICAgICBjb25zdCBzdGF0ZSA9IHsgem9vbTogbWFwLmdldFpvb20oKSB9O1xuICAgICAgICBsZXQgY2hhbmdlZCA9IGZhbHNlO1xuICAgICAgICBpZiAodGhpcy5zdGF0ZS56b29tID49IHRoaXMubWF4Wm9vbSAmJiBzdGF0ZS56b29tID49IHRoaXMubWF4Wm9vbSkgO1xuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNoYW5nZWQgPSAhZXF1YWwodGhpcy5zdGF0ZSwgc3RhdGUpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuc3RhdGUgPSBzdGF0ZTtcbiAgICAgICAgaWYgKG1hcC5nZXRab29tKCkgPj0gdGhpcy5tYXhab29tKSB7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIGNsdXN0ZXJzOiB0aGlzLm5vb3Aoe1xuICAgICAgICAgICAgICAgICAgICBtYXJrZXJzLFxuICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgIGNoYW5nZWQsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBjbHVzdGVyczogdGhpcy5jbHVzdGVyKHtcbiAgICAgICAgICAgICAgICBtYXJrZXJzOiBmaWx0ZXJNYXJrZXJzVG9QYWRkZWRWaWV3cG9ydChtYXAsIG1hcENhbnZhc1Byb2plY3Rpb24sIG1hcmtlcnMsIHRoaXMudmlld3BvcnRQYWRkaW5nKSxcbiAgICAgICAgICAgICAgICBtYXAsXG4gICAgICAgICAgICAgICAgbWFwQ2FudmFzUHJvamVjdGlvbixcbiAgICAgICAgICAgIH0pLFxuICAgICAgICB9O1xuICAgIH1cbiAgICBjbHVzdGVyKHsgbWFya2VycywgbWFwLCBtYXBDYW52YXNQcm9qZWN0aW9uLCB9KSB7XG4gICAgICAgIHRoaXMuY2x1c3RlcnMgPSBbXTtcbiAgICAgICAgbWFya2Vycy5mb3JFYWNoKChtYXJrZXIpID0+IHtcbiAgICAgICAgICAgIHRoaXMuYWRkVG9DbG9zZXN0Q2x1c3RlcihtYXJrZXIsIG1hcCwgbWFwQ2FudmFzUHJvamVjdGlvbik7XG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gdGhpcy5jbHVzdGVycztcbiAgICB9XG4gICAgYWRkVG9DbG9zZXN0Q2x1c3RlcihtYXJrZXIsIG1hcCwgcHJvamVjdGlvbikge1xuICAgICAgICBsZXQgbWF4RGlzdGFuY2UgPSB0aGlzLm1heERpc3RhbmNlOyAvLyBTb21lIGxhcmdlIG51bWJlclxuICAgICAgICBsZXQgY2x1c3RlciA9IG51bGw7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5jbHVzdGVycy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgY29uc3QgY2FuZGlkYXRlID0gdGhpcy5jbHVzdGVyc1tpXTtcbiAgICAgICAgICAgIGNvbnN0IGRpc3RhbmNlID0gZGlzdGFuY2VCZXR3ZWVuUG9pbnRzKGNhbmRpZGF0ZS5ib3VuZHMuZ2V0Q2VudGVyKCkudG9KU09OKCksIE1hcmtlclV0aWxzLmdldFBvc2l0aW9uKG1hcmtlcikudG9KU09OKCkpO1xuICAgICAgICAgICAgaWYgKGRpc3RhbmNlIDwgbWF4RGlzdGFuY2UpIHtcbiAgICAgICAgICAgICAgICBtYXhEaXN0YW5jZSA9IGRpc3RhbmNlO1xuICAgICAgICAgICAgICAgIGNsdXN0ZXIgPSBjYW5kaWRhdGU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGNsdXN0ZXIgJiZcbiAgICAgICAgICAgIGV4dGVuZEJvdW5kc1RvUGFkZGVkVmlld3BvcnQoY2x1c3Rlci5ib3VuZHMsIHByb2plY3Rpb24sIHRoaXMuZ3JpZFNpemUpLmNvbnRhaW5zKE1hcmtlclV0aWxzLmdldFBvc2l0aW9uKG1hcmtlcikpKSB7XG4gICAgICAgICAgICBjbHVzdGVyLnB1c2gobWFya2VyKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNvbnN0IGNsdXN0ZXIgPSBuZXcgQ2x1c3Rlcih7IG1hcmtlcnM6IFttYXJrZXJdIH0pO1xuICAgICAgICAgICAgdGhpcy5jbHVzdGVycy5wdXNoKGNsdXN0ZXIpO1xuICAgICAgICB9XG4gICAgfVxufVxuXG4vKipcbiAqIENvcHlyaWdodCAyMDIxIEdvb2dsZSBMTENcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vKipcbiAqIE5vb3AgYWxnb3JpdGhtIGRvZXMgbm90IGdlbmVyYXRlIGFueSBjbHVzdGVycyBvciBmaWx0ZXIgbWFya2VycyBieSB0aGUgYW4gZXh0ZW5kZWQgdmlld3BvcnQuXG4gKi9cbmNsYXNzIE5vb3BBbGdvcml0aG0gZXh0ZW5kcyBBYnN0cmFjdEFsZ29yaXRobSB7XG4gICAgY29uc3RydWN0b3IoX2EpIHtcbiAgICAgICAgdmFyIG9wdGlvbnMgPSBfX3Jlc3QoX2EsIFtdKTtcbiAgICAgICAgc3VwZXIob3B0aW9ucyk7XG4gICAgfVxuICAgIGNhbGN1bGF0ZSh7IG1hcmtlcnMsIG1hcCwgbWFwQ2FudmFzUHJvamVjdGlvbiwgfSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgY2x1c3RlcnM6IHRoaXMuY2x1c3Rlcih7IG1hcmtlcnMsIG1hcCwgbWFwQ2FudmFzUHJvamVjdGlvbiB9KSxcbiAgICAgICAgICAgIGNoYW5nZWQ6IGZhbHNlLFxuICAgICAgICB9O1xuICAgIH1cbiAgICBjbHVzdGVyKGlucHV0KSB7XG4gICAgICAgIHJldHVybiB0aGlzLm5vb3AoaW5wdXQpO1xuICAgIH1cbn1cblxuLyoqXG4gKiBDb3B5cmlnaHQgMjAyMSBHb29nbGUgTExDXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuLyoqXG4gKiBBIHZlcnkgZmFzdCBKYXZhU2NyaXB0IGFsZ29yaXRobSBmb3IgZ2Vvc3BhdGlhbCBwb2ludCBjbHVzdGVyaW5nIHVzaW5nIEtEIHRyZWVzLlxuICpcbiAqIEBzZWUgaHR0cHM6Ly93d3cubnBtanMuY29tL3BhY2thZ2Uvc3VwZXJjbHVzdGVyIGZvciBtb3JlIGluZm9ybWF0aW9uIG9uIG9wdGlvbnMuXG4gKi9cbmNsYXNzIFN1cGVyQ2x1c3RlckFsZ29yaXRobSBleHRlbmRzIEFic3RyYWN0QWxnb3JpdGhtIHtcbiAgICBjb25zdHJ1Y3RvcihfYSkge1xuICAgICAgICB2YXIgeyBtYXhab29tLCByYWRpdXMgPSA2MCB9ID0gX2EsIG9wdGlvbnMgPSBfX3Jlc3QoX2EsIFtcIm1heFpvb21cIiwgXCJyYWRpdXNcIl0pO1xuICAgICAgICBzdXBlcih7IG1heFpvb20gfSk7XG4gICAgICAgIHRoaXMuc3RhdGUgPSB7IHpvb206IC0xIH07XG4gICAgICAgIHRoaXMuc3VwZXJDbHVzdGVyID0gbmV3IFN1cGVyQ2x1c3RlcihPYmplY3QuYXNzaWduKHsgbWF4Wm9vbTogdGhpcy5tYXhab29tLCByYWRpdXMgfSwgb3B0aW9ucykpO1xuICAgIH1cbiAgICBjYWxjdWxhdGUoaW5wdXQpIHtcbiAgICAgICAgbGV0IGNoYW5nZWQgPSBmYWxzZTtcbiAgICAgICAgY29uc3Qgc3RhdGUgPSB7IHpvb206IGlucHV0Lm1hcC5nZXRab29tKCkgfTtcbiAgICAgICAgaWYgKCFlcXVhbChpbnB1dC5tYXJrZXJzLCB0aGlzLm1hcmtlcnMpKSB7XG4gICAgICAgICAgICBjaGFuZ2VkID0gdHJ1ZTtcbiAgICAgICAgICAgIC8vIFRPRE8gdXNlIHByb3h5IHRvIGF2b2lkIGNvcHk/XG4gICAgICAgICAgICB0aGlzLm1hcmtlcnMgPSBbLi4uaW5wdXQubWFya2Vyc107XG4gICAgICAgICAgICBjb25zdCBwb2ludHMgPSB0aGlzLm1hcmtlcnMubWFwKChtYXJrZXIpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBwb3NpdGlvbiA9IE1hcmtlclV0aWxzLmdldFBvc2l0aW9uKG1hcmtlcik7XG4gICAgICAgICAgICAgICAgY29uc3QgY29vcmRpbmF0ZXMgPSBbcG9zaXRpb24ubG5nKCksIHBvc2l0aW9uLmxhdCgpXTtcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICB0eXBlOiBcIkZlYXR1cmVcIixcbiAgICAgICAgICAgICAgICAgICAgZ2VvbWV0cnk6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6IFwiUG9pbnRcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvb3JkaW5hdGVzLFxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICBwcm9wZXJ0aWVzOiB7IG1hcmtlciB9LFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRoaXMuc3VwZXJDbHVzdGVyLmxvYWQocG9pbnRzKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIWNoYW5nZWQpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLnN0YXRlLnpvb20gPD0gdGhpcy5tYXhab29tIHx8IHN0YXRlLnpvb20gPD0gdGhpcy5tYXhab29tKSB7XG4gICAgICAgICAgICAgICAgY2hhbmdlZCA9ICFlcXVhbCh0aGlzLnN0YXRlLCBzdGF0ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zdGF0ZSA9IHN0YXRlO1xuICAgICAgICBpZiAoY2hhbmdlZCkge1xuICAgICAgICAgICAgdGhpcy5jbHVzdGVycyA9IHRoaXMuY2x1c3RlcihpbnB1dCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHsgY2x1c3RlcnM6IHRoaXMuY2x1c3RlcnMsIGNoYW5nZWQgfTtcbiAgICB9XG4gICAgY2x1c3Rlcih7IG1hcCB9KSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN1cGVyQ2x1c3RlclxuICAgICAgICAgICAgLmdldENsdXN0ZXJzKFstMTgwLCAtOTAsIDE4MCwgOTBdLCBNYXRoLnJvdW5kKG1hcC5nZXRab29tKCkpKVxuICAgICAgICAgICAgLm1hcCgoZmVhdHVyZSkgPT4gdGhpcy50cmFuc2Zvcm1DbHVzdGVyKGZlYXR1cmUpKTtcbiAgICB9XG4gICAgdHJhbnNmb3JtQ2x1c3Rlcih7IGdlb21ldHJ5OiB7IGNvb3JkaW5hdGVzOiBbbG5nLCBsYXRdLCB9LCBwcm9wZXJ0aWVzLCB9KSB7XG4gICAgICAgIGlmIChwcm9wZXJ0aWVzLmNsdXN0ZXIpIHtcbiAgICAgICAgICAgIHJldHVybiBuZXcgQ2x1c3Rlcih7XG4gICAgICAgICAgICAgICAgbWFya2VyczogdGhpcy5zdXBlckNsdXN0ZXJcbiAgICAgICAgICAgICAgICAgICAgLmdldExlYXZlcyhwcm9wZXJ0aWVzLmNsdXN0ZXJfaWQsIEluZmluaXR5KVxuICAgICAgICAgICAgICAgICAgICAubWFwKChsZWFmKSA9PiBsZWFmLnByb3BlcnRpZXMubWFya2VyKSxcbiAgICAgICAgICAgICAgICBwb3NpdGlvbjogeyBsYXQsIGxuZyB9LFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgbWFya2VyID0gcHJvcGVydGllcy5tYXJrZXI7XG4gICAgICAgIHJldHVybiBuZXcgQ2x1c3Rlcih7XG4gICAgICAgICAgICBtYXJrZXJzOiBbbWFya2VyXSxcbiAgICAgICAgICAgIHBvc2l0aW9uOiBNYXJrZXJVdGlscy5nZXRQb3NpdGlvbihtYXJrZXIpLFxuICAgICAgICB9KTtcbiAgICB9XG59XG5cbi8qKlxuICogQ29weXJpZ2h0IDIwMjEgR29vZ2xlIExMQ1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8qKlxuICogQSB2ZXJ5IGZhc3QgSmF2YVNjcmlwdCBhbGdvcml0aG0gZm9yIGdlb3NwYXRpYWwgcG9pbnQgY2x1c3RlcmluZyB1c2luZyBLRCB0cmVlcy5cbiAqXG4gKiBAc2VlIGh0dHBzOi8vd3d3Lm5wbWpzLmNvbS9wYWNrYWdlL3N1cGVyY2x1c3RlciBmb3IgbW9yZSBpbmZvcm1hdGlvbiBvbiBvcHRpb25zLlxuICovXG5jbGFzcyBTdXBlckNsdXN0ZXJWaWV3cG9ydEFsZ29yaXRobSBleHRlbmRzIEFic3RyYWN0Vmlld3BvcnRBbGdvcml0aG0ge1xuICAgIGNvbnN0cnVjdG9yKF9hKSB7XG4gICAgICAgIHZhciB7IG1heFpvb20sIHJhZGl1cyA9IDYwLCB2aWV3cG9ydFBhZGRpbmcgPSA2MCB9ID0gX2EsIG9wdGlvbnMgPSBfX3Jlc3QoX2EsIFtcIm1heFpvb21cIiwgXCJyYWRpdXNcIiwgXCJ2aWV3cG9ydFBhZGRpbmdcIl0pO1xuICAgICAgICBzdXBlcih7IG1heFpvb20sIHZpZXdwb3J0UGFkZGluZyB9KTtcbiAgICAgICAgdGhpcy5zdXBlckNsdXN0ZXIgPSBuZXcgU3VwZXJDbHVzdGVyKE9iamVjdC5hc3NpZ24oeyBtYXhab29tOiB0aGlzLm1heFpvb20sIHJhZGl1cyB9LCBvcHRpb25zKSk7XG4gICAgICAgIHRoaXMuc3RhdGUgPSB7IHpvb206IC0xLCB2aWV3OiBbMCwgMCwgMCwgMF0gfTtcbiAgICB9XG4gICAgY2FsY3VsYXRlKGlucHV0KSB7XG4gICAgICAgIGNvbnN0IHN0YXRlID0ge1xuICAgICAgICAgICAgem9vbTogTWF0aC5yb3VuZChpbnB1dC5tYXAuZ2V0Wm9vbSgpKSxcbiAgICAgICAgICAgIHZpZXc6IGdldFBhZGRlZFZpZXdwb3J0KGlucHV0Lm1hcC5nZXRCb3VuZHMoKSwgaW5wdXQubWFwQ2FudmFzUHJvamVjdGlvbiwgdGhpcy52aWV3cG9ydFBhZGRpbmcpLFxuICAgICAgICB9O1xuICAgICAgICBsZXQgY2hhbmdlZCA9ICFlcXVhbCh0aGlzLnN0YXRlLCBzdGF0ZSk7XG4gICAgICAgIGlmICghZXF1YWwoaW5wdXQubWFya2VycywgdGhpcy5tYXJrZXJzKSkge1xuICAgICAgICAgICAgY2hhbmdlZCA9IHRydWU7XG4gICAgICAgICAgICAvLyBUT0RPIHVzZSBwcm94eSB0byBhdm9pZCBjb3B5P1xuICAgICAgICAgICAgdGhpcy5tYXJrZXJzID0gWy4uLmlucHV0Lm1hcmtlcnNdO1xuICAgICAgICAgICAgY29uc3QgcG9pbnRzID0gdGhpcy5tYXJrZXJzLm1hcCgobWFya2VyKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgcG9zaXRpb24gPSBNYXJrZXJVdGlscy5nZXRQb3NpdGlvbihtYXJrZXIpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGNvb3JkaW5hdGVzID0gW3Bvc2l0aW9uLmxuZygpLCBwb3NpdGlvbi5sYXQoKV07XG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogXCJGZWF0dXJlXCIsXG4gICAgICAgICAgICAgICAgICAgIGdlb21ldHJ5OiB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiBcIlBvaW50XCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb29yZGluYXRlcyxcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgcHJvcGVydGllczogeyBtYXJrZXIgfSxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB0aGlzLnN1cGVyQ2x1c3Rlci5sb2FkKHBvaW50cyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGNoYW5nZWQpIHtcbiAgICAgICAgICAgIHRoaXMuY2x1c3RlcnMgPSB0aGlzLmNsdXN0ZXIoaW5wdXQpO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IHN0YXRlO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB7IGNsdXN0ZXJzOiB0aGlzLmNsdXN0ZXJzLCBjaGFuZ2VkIH07XG4gICAgfVxuICAgIGNsdXN0ZXIoeyBtYXAsIG1hcENhbnZhc1Byb2plY3Rpb24gfSkge1xuICAgICAgICAvKiByZWNhbGN1bGF0ZSBuZXcgc3RhdGUgYmVjYXVzZSB3ZSBjYW4ndCB1c2UgdGhlIGNhY2hlZCB2ZXJzaW9uLiAqL1xuICAgICAgICBjb25zdCBzdGF0ZSA9IHtcbiAgICAgICAgICAgIHpvb206IE1hdGgucm91bmQobWFwLmdldFpvb20oKSksXG4gICAgICAgICAgICB2aWV3OiBnZXRQYWRkZWRWaWV3cG9ydChtYXAuZ2V0Qm91bmRzKCksIG1hcENhbnZhc1Byb2plY3Rpb24sIHRoaXMudmlld3BvcnRQYWRkaW5nKSxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3VwZXJDbHVzdGVyXG4gICAgICAgICAgICAuZ2V0Q2x1c3RlcnMoc3RhdGUudmlldywgc3RhdGUuem9vbSlcbiAgICAgICAgICAgIC5tYXAoKGZlYXR1cmUpID0+IHRoaXMudHJhbnNmb3JtQ2x1c3RlcihmZWF0dXJlKSk7XG4gICAgfVxuICAgIHRyYW5zZm9ybUNsdXN0ZXIoeyBnZW9tZXRyeTogeyBjb29yZGluYXRlczogW2xuZywgbGF0XSwgfSwgcHJvcGVydGllcywgfSkge1xuICAgICAgICBpZiAocHJvcGVydGllcy5jbHVzdGVyKSB7XG4gICAgICAgICAgICByZXR1cm4gbmV3IENsdXN0ZXIoe1xuICAgICAgICAgICAgICAgIG1hcmtlcnM6IHRoaXMuc3VwZXJDbHVzdGVyXG4gICAgICAgICAgICAgICAgICAgIC5nZXRMZWF2ZXMocHJvcGVydGllcy5jbHVzdGVyX2lkLCBJbmZpbml0eSlcbiAgICAgICAgICAgICAgICAgICAgLm1hcCgobGVhZikgPT4gbGVhZi5wcm9wZXJ0aWVzLm1hcmtlciksXG4gICAgICAgICAgICAgICAgcG9zaXRpb246IHsgbGF0LCBsbmcgfSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IG1hcmtlciA9IHByb3BlcnRpZXMubWFya2VyO1xuICAgICAgICByZXR1cm4gbmV3IENsdXN0ZXIoe1xuICAgICAgICAgICAgbWFya2VyczogW21hcmtlcl0sXG4gICAgICAgICAgICBwb3NpdGlvbjogTWFya2VyVXRpbHMuZ2V0UG9zaXRpb24obWFya2VyKSxcbiAgICAgICAgfSk7XG4gICAgfVxufVxuXG4vKipcbiAqIENvcHlyaWdodCAyMDIxIEdvb2dsZSBMTENcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vKipcbiAqIFByb3ZpZGVzIHN0YXRpc3RpY3Mgb24gYWxsIGNsdXN0ZXJzIGluIHRoZSBjdXJyZW50IHJlbmRlciBjeWNsZSBmb3IgdXNlIGluIHtAbGluayBSZW5kZXJlci5yZW5kZXJ9LlxuICovXG5jbGFzcyBDbHVzdGVyU3RhdHMge1xuICAgIGNvbnN0cnVjdG9yKG1hcmtlcnMsIGNsdXN0ZXJzKSB7XG4gICAgICAgIHRoaXMubWFya2VycyA9IHsgc3VtOiBtYXJrZXJzLmxlbmd0aCB9O1xuICAgICAgICBjb25zdCBjbHVzdGVyTWFya2VyQ291bnRzID0gY2x1c3RlcnMubWFwKChhKSA9PiBhLmNvdW50KTtcbiAgICAgICAgY29uc3QgY2x1c3Rlck1hcmtlclN1bSA9IGNsdXN0ZXJNYXJrZXJDb3VudHMucmVkdWNlKChhLCBiKSA9PiBhICsgYiwgMCk7XG4gICAgICAgIHRoaXMuY2x1c3RlcnMgPSB7XG4gICAgICAgICAgICBjb3VudDogY2x1c3RlcnMubGVuZ3RoLFxuICAgICAgICAgICAgbWFya2Vyczoge1xuICAgICAgICAgICAgICAgIG1lYW46IGNsdXN0ZXJNYXJrZXJTdW0gLyBjbHVzdGVycy5sZW5ndGgsXG4gICAgICAgICAgICAgICAgc3VtOiBjbHVzdGVyTWFya2VyU3VtLFxuICAgICAgICAgICAgICAgIG1pbjogTWF0aC5taW4oLi4uY2x1c3Rlck1hcmtlckNvdW50cyksXG4gICAgICAgICAgICAgICAgbWF4OiBNYXRoLm1heCguLi5jbHVzdGVyTWFya2VyQ291bnRzKSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH07XG4gICAgfVxufVxuY2xhc3MgRGVmYXVsdFJlbmRlcmVyIHtcbiAgICAvKipcbiAgICAgKiBUaGUgZGVmYXVsdCByZW5kZXIgZnVuY3Rpb24gZm9yIHRoZSBsaWJyYXJ5IHVzZWQgYnkge0BsaW5rIE1hcmtlckNsdXN0ZXJlcn0uXG4gICAgICpcbiAgICAgKiBDdXJyZW50bHkgc2V0IHRvIHVzZSB0aGUgZm9sbG93aW5nOlxuICAgICAqXG4gICAgICogYGBgdHlwZXNjcmlwdFxuICAgICAqIC8vIGNoYW5nZSBjb2xvciBpZiB0aGlzIGNsdXN0ZXIgaGFzIG1vcmUgbWFya2VycyB0aGFuIHRoZSBtZWFuIGNsdXN0ZXJcbiAgICAgKiBjb25zdCBjb2xvciA9XG4gICAgICogICBjb3VudCA+IE1hdGgubWF4KDEwLCBzdGF0cy5jbHVzdGVycy5tYXJrZXJzLm1lYW4pXG4gICAgICogICAgID8gXCIjZmYwMDAwXCJcbiAgICAgKiAgICAgOiBcIiMwMDAwZmZcIjtcbiAgICAgKlxuICAgICAqIC8vIGNyZWF0ZSBzdmcgdXJsIHdpdGggZmlsbCBjb2xvclxuICAgICAqIGNvbnN0IHN2ZyA9IHdpbmRvdy5idG9hKGBcbiAgICAgKiA8c3ZnIGZpbGw9XCIke2NvbG9yfVwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiB2aWV3Qm94PVwiMCAwIDI0MCAyNDBcIj5cbiAgICAgKiAgIDxjaXJjbGUgY3g9XCIxMjBcIiBjeT1cIjEyMFwiIG9wYWNpdHk9XCIuNlwiIHI9XCI3MFwiIC8+XG4gICAgICogICA8Y2lyY2xlIGN4PVwiMTIwXCIgY3k9XCIxMjBcIiBvcGFjaXR5PVwiLjNcIiByPVwiOTBcIiAvPlxuICAgICAqICAgPGNpcmNsZSBjeD1cIjEyMFwiIGN5PVwiMTIwXCIgb3BhY2l0eT1cIi4yXCIgcj1cIjExMFwiIC8+XG4gICAgICogICA8Y2lyY2xlIGN4PVwiMTIwXCIgY3k9XCIxMjBcIiBvcGFjaXR5PVwiLjFcIiByPVwiMTMwXCIgLz5cbiAgICAgKiA8L3N2Zz5gKTtcbiAgICAgKlxuICAgICAqIC8vIGNyZWF0ZSBtYXJrZXIgdXNpbmcgc3ZnIGljb25cbiAgICAgKiByZXR1cm4gbmV3IGdvb2dsZS5tYXBzLk1hcmtlcih7XG4gICAgICogICBwb3NpdGlvbixcbiAgICAgKiAgIGljb246IHtcbiAgICAgKiAgICAgdXJsOiBgZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCwke3N2Z31gLFxuICAgICAqICAgICBzY2FsZWRTaXplOiBuZXcgZ29vZ2xlLm1hcHMuU2l6ZSg0NSwgNDUpLFxuICAgICAqICAgfSxcbiAgICAgKiAgIGxhYmVsOiB7XG4gICAgICogICAgIHRleHQ6IFN0cmluZyhjb3VudCksXG4gICAgICogICAgIGNvbG9yOiBcInJnYmEoMjU1LDI1NSwyNTUsMC45KVwiLFxuICAgICAqICAgICBmb250U2l6ZTogXCIxMnB4XCIsXG4gICAgICogICB9LFxuICAgICAqICAgLy8gYWRqdXN0IHpJbmRleCB0byBiZSBhYm92ZSBvdGhlciBtYXJrZXJzXG4gICAgICogICB6SW5kZXg6IDEwMDAgKyBjb3VudCxcbiAgICAgKiB9KTtcbiAgICAgKiBgYGBcbiAgICAgKi9cbiAgICByZW5kZXIoeyBjb3VudCwgcG9zaXRpb24gfSwgc3RhdHMsIG1hcCkge1xuICAgICAgICAvLyBjaGFuZ2UgY29sb3IgaWYgdGhpcyBjbHVzdGVyIGhhcyBtb3JlIG1hcmtlcnMgdGhhbiB0aGUgbWVhbiBjbHVzdGVyXG4gICAgICAgIGNvbnN0IGNvbG9yID0gY291bnQgPiBNYXRoLm1heCgxMCwgc3RhdHMuY2x1c3RlcnMubWFya2Vycy5tZWFuKSA/IFwiI2ZmMDAwMFwiIDogXCIjMDAwMGZmXCI7XG4gICAgICAgIC8vIGNyZWF0ZSBzdmcgbGl0ZXJhbCB3aXRoIGZpbGwgY29sb3JcbiAgICAgICAgY29uc3Qgc3ZnID0gYDxzdmcgZmlsbD1cIiR7Y29sb3J9XCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIHZpZXdCb3g9XCIwIDAgMjQwIDI0MFwiIHdpZHRoPVwiNTBcIiBoZWlnaHQ9XCI1MFwiPlxuPGNpcmNsZSBjeD1cIjEyMFwiIGN5PVwiMTIwXCIgb3BhY2l0eT1cIi42XCIgcj1cIjcwXCIgLz5cbjxjaXJjbGUgY3g9XCIxMjBcIiBjeT1cIjEyMFwiIG9wYWNpdHk9XCIuM1wiIHI9XCI5MFwiIC8+XG48Y2lyY2xlIGN4PVwiMTIwXCIgY3k9XCIxMjBcIiBvcGFjaXR5PVwiLjJcIiByPVwiMTEwXCIgLz5cbjx0ZXh0IHg9XCI1MCVcIiB5PVwiNTAlXCIgc3R5bGU9XCJmaWxsOiNmZmZcIiB0ZXh0LWFuY2hvcj1cIm1pZGRsZVwiIGZvbnQtc2l6ZT1cIjUwXCIgZG9taW5hbnQtYmFzZWxpbmU9XCJtaWRkbGVcIiBmb250LWZhbWlseT1cInJvYm90byxhcmlhbCxzYW5zLXNlcmlmXCI+JHtjb3VudH08L3RleHQ+XG48L3N2Zz5gO1xuICAgICAgICBjb25zdCB0aXRsZSA9IGBDbHVzdGVyIG9mICR7Y291bnR9IG1hcmtlcnNgLCBcbiAgICAgICAgLy8gYWRqdXN0IHpJbmRleCB0byBiZSBhYm92ZSBvdGhlciBtYXJrZXJzXG4gICAgICAgIHpJbmRleCA9IE51bWJlcihnb29nbGUubWFwcy5NYXJrZXIuTUFYX1pJTkRFWCkgKyBjb3VudDtcbiAgICAgICAgaWYgKE1hcmtlclV0aWxzLmlzQWR2YW5jZWRNYXJrZXJBdmFpbGFibGUobWFwKSkge1xuICAgICAgICAgICAgLy8gY3JlYXRlIGNsdXN0ZXIgU1ZHIGVsZW1lbnRcbiAgICAgICAgICAgIGNvbnN0IHBhcnNlciA9IG5ldyBET01QYXJzZXIoKTtcbiAgICAgICAgICAgIGNvbnN0IHN2Z0VsID0gcGFyc2VyLnBhcnNlRnJvbVN0cmluZyhzdmcsIFwiaW1hZ2Uvc3ZnK3htbFwiKS5kb2N1bWVudEVsZW1lbnQ7XG4gICAgICAgICAgICBzdmdFbC5zZXRBdHRyaWJ1dGUoXCJ0cmFuc2Zvcm1cIiwgXCJ0cmFuc2xhdGUoMCAyNSlcIik7XG4gICAgICAgICAgICBjb25zdCBjbHVzdGVyT3B0aW9ucyA9IHtcbiAgICAgICAgICAgICAgICBtYXAsXG4gICAgICAgICAgICAgICAgcG9zaXRpb24sXG4gICAgICAgICAgICAgICAgekluZGV4LFxuICAgICAgICAgICAgICAgIHRpdGxlLFxuICAgICAgICAgICAgICAgIGNvbnRlbnQ6IHN2Z0VsLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIHJldHVybiBuZXcgZ29vZ2xlLm1hcHMubWFya2VyLkFkdmFuY2VkTWFya2VyRWxlbWVudChjbHVzdGVyT3B0aW9ucyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgY2x1c3Rlck9wdGlvbnMgPSB7XG4gICAgICAgICAgICBwb3NpdGlvbixcbiAgICAgICAgICAgIHpJbmRleCxcbiAgICAgICAgICAgIHRpdGxlLFxuICAgICAgICAgICAgaWNvbjoge1xuICAgICAgICAgICAgICAgIHVybDogYGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsJHtidG9hKHN2Zyl9YCxcbiAgICAgICAgICAgICAgICBhbmNob3I6IG5ldyBnb29nbGUubWFwcy5Qb2ludCgyNSwgMjUpLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIG5ldyBnb29nbGUubWFwcy5NYXJrZXIoY2x1c3Rlck9wdGlvbnMpO1xuICAgIH1cbn1cblxuLyoqXG4gKiBDb3B5cmlnaHQgMjAxOSBHb29nbGUgTExDLiBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8qKlxuICogRXh0ZW5kcyBhbiBvYmplY3QncyBwcm90b3R5cGUgYnkgYW5vdGhlcidzLlxuICpcbiAqIEBwYXJhbSB0eXBlMSBUaGUgVHlwZSB0byBiZSBleHRlbmRlZC5cbiAqIEBwYXJhbSB0eXBlMiBUaGUgVHlwZSB0byBleHRlbmQgd2l0aC5cbiAqIEBpZ25vcmVcbiAqL1xuLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnlcbmZ1bmN0aW9uIGV4dGVuZCh0eXBlMSwgdHlwZTIpIHtcbiAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBwcmVmZXItY29uc3RcbiAgICBmb3IgKGxldCBwcm9wZXJ0eSBpbiB0eXBlMi5wcm90b3R5cGUpIHtcbiAgICAgICAgdHlwZTEucHJvdG90eXBlW3Byb3BlcnR5XSA9IHR5cGUyLnByb3RvdHlwZVtwcm9wZXJ0eV07XG4gICAgfVxufVxuLyoqXG4gKiBAaWdub3JlXG4gKi9cbmNsYXNzIE92ZXJsYXlWaWV3U2FmZSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIC8vIE1hcmtlckNsdXN0ZXJlciBpbXBsZW1lbnRzIGdvb2dsZS5tYXBzLk92ZXJsYXlWaWV3IGludGVyZmFjZS4gV2UgdXNlIHRoZVxuICAgICAgICAvLyBleHRlbmQgZnVuY3Rpb24gdG8gZXh0ZW5kIE1hcmtlckNsdXN0ZXJlciB3aXRoIGdvb2dsZS5tYXBzLk92ZXJsYXlWaWV3XG4gICAgICAgIC8vIGJlY2F1c2UgaXQgbWlnaHQgbm90IGFsd2F5cyBiZSBhdmFpbGFibGUgd2hlbiB0aGUgY29kZSBpcyBkZWZpbmVkIHNvIHdlXG4gICAgICAgIC8vIGxvb2sgZm9yIGl0IGF0IHRoZSBsYXN0IHBvc3NpYmxlIG1vbWVudC4gSWYgaXQgZG9lc24ndCBleGlzdCBub3cgdGhlblxuICAgICAgICAvLyB0aGVyZSBpcyBubyBwb2ludCBnb2luZyBhaGVhZCA6KVxuICAgICAgICBleHRlbmQoT3ZlcmxheVZpZXdTYWZlLCBnb29nbGUubWFwcy5PdmVybGF5Vmlldyk7XG4gICAgfVxufVxuXG4vKipcbiAqIENvcHlyaWdodCAyMDIxIEdvb2dsZSBMTENcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG52YXIgTWFya2VyQ2x1c3RlcmVyRXZlbnRzO1xuKGZ1bmN0aW9uIChNYXJrZXJDbHVzdGVyZXJFdmVudHMpIHtcbiAgICBNYXJrZXJDbHVzdGVyZXJFdmVudHNbXCJDTFVTVEVSSU5HX0JFR0lOXCJdID0gXCJjbHVzdGVyaW5nYmVnaW5cIjtcbiAgICBNYXJrZXJDbHVzdGVyZXJFdmVudHNbXCJDTFVTVEVSSU5HX0VORFwiXSA9IFwiY2x1c3RlcmluZ2VuZFwiO1xuICAgIE1hcmtlckNsdXN0ZXJlckV2ZW50c1tcIkNMVVNURVJfQ0xJQ0tcIl0gPSBcImNsaWNrXCI7XG59KShNYXJrZXJDbHVzdGVyZXJFdmVudHMgfHwgKE1hcmtlckNsdXN0ZXJlckV2ZW50cyA9IHt9KSk7XG5jb25zdCBkZWZhdWx0T25DbHVzdGVyQ2xpY2tIYW5kbGVyID0gKF8sIGNsdXN0ZXIsIG1hcCkgPT4ge1xuICAgIG1hcC5maXRCb3VuZHMoY2x1c3Rlci5ib3VuZHMpO1xufTtcbi8qKlxuICogTWFya2VyQ2x1c3RlcmVyIGNyZWF0ZXMgYW5kIG1hbmFnZXMgcGVyLXpvb20tbGV2ZWwgY2x1c3RlcnMgZm9yIGxhcmdlIGFtb3VudHNcbiAqIG9mIG1hcmtlcnMuIFNlZSB7QGxpbmsgTWFya2VyQ2x1c3RlcmVyT3B0aW9uc30gZm9yIG1vcmUgZGV0YWlscy5cbiAqXG4gKi9cbmNsYXNzIE1hcmtlckNsdXN0ZXJlciBleHRlbmRzIE92ZXJsYXlWaWV3U2FmZSB7XG4gICAgY29uc3RydWN0b3IoeyBtYXAsIG1hcmtlcnMgPSBbXSwgYWxnb3JpdGhtT3B0aW9ucyA9IHt9LCBhbGdvcml0aG0gPSBuZXcgU3VwZXJDbHVzdGVyQWxnb3JpdGhtKGFsZ29yaXRobU9wdGlvbnMpLCByZW5kZXJlciA9IG5ldyBEZWZhdWx0UmVuZGVyZXIoKSwgb25DbHVzdGVyQ2xpY2sgPSBkZWZhdWx0T25DbHVzdGVyQ2xpY2tIYW5kbGVyLCB9KSB7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgICAgIHRoaXMubWFya2VycyA9IFsuLi5tYXJrZXJzXTtcbiAgICAgICAgdGhpcy5jbHVzdGVycyA9IFtdO1xuICAgICAgICB0aGlzLmFsZ29yaXRobSA9IGFsZ29yaXRobTtcbiAgICAgICAgdGhpcy5yZW5kZXJlciA9IHJlbmRlcmVyO1xuICAgICAgICB0aGlzLm9uQ2x1c3RlckNsaWNrID0gb25DbHVzdGVyQ2xpY2s7XG4gICAgICAgIGlmIChtYXApIHtcbiAgICAgICAgICAgIHRoaXMuc2V0TWFwKG1hcCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgYWRkTWFya2VyKG1hcmtlciwgbm9EcmF3KSB7XG4gICAgICAgIGlmICh0aGlzLm1hcmtlcnMuaW5jbHVkZXMobWFya2VyKSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMubWFya2Vycy5wdXNoKG1hcmtlcik7XG4gICAgICAgIGlmICghbm9EcmF3KSB7XG4gICAgICAgICAgICB0aGlzLnJlbmRlcigpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGFkZE1hcmtlcnMobWFya2Vycywgbm9EcmF3KSB7XG4gICAgICAgIG1hcmtlcnMuZm9yRWFjaCgobWFya2VyKSA9PiB7XG4gICAgICAgICAgICB0aGlzLmFkZE1hcmtlcihtYXJrZXIsIHRydWUpO1xuICAgICAgICB9KTtcbiAgICAgICAgaWYgKCFub0RyYXcpIHtcbiAgICAgICAgICAgIHRoaXMucmVuZGVyKCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmVtb3ZlTWFya2VyKG1hcmtlciwgbm9EcmF3KSB7XG4gICAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5tYXJrZXJzLmluZGV4T2YobWFya2VyKTtcbiAgICAgICAgaWYgKGluZGV4ID09PSAtMSkge1xuICAgICAgICAgICAgLy8gTWFya2VyIGlzIG5vdCBpbiBvdXIgbGlzdCBvZiBtYXJrZXJzLCBzbyBkbyBub3RoaW5nOlxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIE1hcmtlclV0aWxzLnNldE1hcChtYXJrZXIsIG51bGwpO1xuICAgICAgICB0aGlzLm1hcmtlcnMuc3BsaWNlKGluZGV4LCAxKTsgLy8gUmVtb3ZlIHRoZSBtYXJrZXIgZnJvbSB0aGUgbGlzdCBvZiBtYW5hZ2VkIG1hcmtlcnNcbiAgICAgICAgaWYgKCFub0RyYXcpIHtcbiAgICAgICAgICAgIHRoaXMucmVuZGVyKCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIHJlbW92ZU1hcmtlcnMobWFya2Vycywgbm9EcmF3KSB7XG4gICAgICAgIGxldCByZW1vdmVkID0gZmFsc2U7XG4gICAgICAgIG1hcmtlcnMuZm9yRWFjaCgobWFya2VyKSA9PiB7XG4gICAgICAgICAgICByZW1vdmVkID0gdGhpcy5yZW1vdmVNYXJrZXIobWFya2VyLCB0cnVlKSB8fCByZW1vdmVkO1xuICAgICAgICB9KTtcbiAgICAgICAgaWYgKHJlbW92ZWQgJiYgIW5vRHJhdykge1xuICAgICAgICAgICAgdGhpcy5yZW5kZXIoKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVtb3ZlZDtcbiAgICB9XG4gICAgY2xlYXJNYXJrZXJzKG5vRHJhdykge1xuICAgICAgICB0aGlzLm1hcmtlcnMubGVuZ3RoID0gMDtcbiAgICAgICAgaWYgKCFub0RyYXcpIHtcbiAgICAgICAgICAgIHRoaXMucmVuZGVyKCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVjYWxjdWxhdGVzIGFuZCBkcmF3cyBhbGwgdGhlIG1hcmtlciBjbHVzdGVycy5cbiAgICAgKi9cbiAgICByZW5kZXIoKSB7XG4gICAgICAgIGNvbnN0IG1hcCA9IHRoaXMuZ2V0TWFwKCk7XG4gICAgICAgIGlmIChtYXAgaW5zdGFuY2VvZiBnb29nbGUubWFwcy5NYXAgJiYgbWFwLmdldFByb2plY3Rpb24oKSkge1xuICAgICAgICAgICAgZ29vZ2xlLm1hcHMuZXZlbnQudHJpZ2dlcih0aGlzLCBNYXJrZXJDbHVzdGVyZXJFdmVudHMuQ0xVU1RFUklOR19CRUdJTiwgdGhpcyk7XG4gICAgICAgICAgICBjb25zdCB7IGNsdXN0ZXJzLCBjaGFuZ2VkIH0gPSB0aGlzLmFsZ29yaXRobS5jYWxjdWxhdGUoe1xuICAgICAgICAgICAgICAgIG1hcmtlcnM6IHRoaXMubWFya2VycyxcbiAgICAgICAgICAgICAgICBtYXAsXG4gICAgICAgICAgICAgICAgbWFwQ2FudmFzUHJvamVjdGlvbjogdGhpcy5nZXRQcm9qZWN0aW9uKCksXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIC8vIEFsbG93IGFsZ29yaXRobXMgdG8gcmV0dXJuIGZsYWcgb24gd2hldGhlciB0aGUgY2x1c3RlcnMvbWFya2VycyBoYXZlIGNoYW5nZWQuXG4gICAgICAgICAgICBpZiAoY2hhbmdlZCB8fCBjaGFuZ2VkID09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgIC8vIEFjY3VtdWxhdGUgdGhlIG1hcmtlcnMgb2YgdGhlIGNsdXN0ZXJzIGNvbXBvc2VkIG9mIGEgc2luZ2xlIG1hcmtlci5cbiAgICAgICAgICAgICAgICAvLyBUaG9zZSBjbHVzdGVycyBkaXJlY3RseSB1c2UgdGhlIG1hcmtlci5cbiAgICAgICAgICAgICAgICAvLyBDbHVzdGVycyB3aXRoIG1vcmUgdGhhbiBvbmUgbWFya2VycyB1c2UgYSBncm91cCBtYXJrZXIgZ2VuZXJhdGVkIGJ5IGEgcmVuZGVyZXIuXG4gICAgICAgICAgICAgICAgY29uc3Qgc2luZ2xlTWFya2VyID0gbmV3IFNldCgpO1xuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgY2x1c3RlciBvZiBjbHVzdGVycykge1xuICAgICAgICAgICAgICAgICAgICBpZiAoY2x1c3Rlci5tYXJrZXJzLmxlbmd0aCA9PSAxKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzaW5nbGVNYXJrZXIuYWRkKGNsdXN0ZXIubWFya2Vyc1swXSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgZ3JvdXBNYXJrZXJzID0gW107XG4gICAgICAgICAgICAgICAgLy8gSXRlcmF0ZSB0aGUgY2x1c3RlcnMgdGhhdCBhcmUgY3VycmVudGx5IHJlbmRlcmVkLlxuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgY2x1c3RlciBvZiB0aGlzLmNsdXN0ZXJzKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChjbHVzdGVyLm1hcmtlciA9PSBudWxsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoY2x1c3Rlci5tYXJrZXJzLmxlbmd0aCA9PSAxKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXNpbmdsZU1hcmtlci5oYXMoY2x1c3Rlci5tYXJrZXIpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gVGhlIG1hcmtlcjpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyAtIHdhcyBwcmV2aW91c2x5IHJlbmRlcmVkIGJlY2F1c2UgaXQgaXMgZnJvbSBhIGNsdXN0ZXIgd2l0aCAxIG1hcmtlcixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyAtIHNob3VsZCBubyBtb3JlIGJlIHJlbmRlcmVkIGFzIGl0IGlzIG5vdCBpbiBzaW5nbGVNYXJrZXIuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgTWFya2VyVXRpbHMuc2V0TWFwKGNsdXN0ZXIubWFya2VyLCBudWxsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIERlbGF5IHRoZSByZW1vdmFsIG9mIG9sZCBncm91cCBtYXJrZXJzIHRvIGF2b2lkIGZsaWNrZXJpbmcuXG4gICAgICAgICAgICAgICAgICAgICAgICBncm91cE1hcmtlcnMucHVzaChjbHVzdGVyLm1hcmtlcik7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy5jbHVzdGVycyA9IGNsdXN0ZXJzO1xuICAgICAgICAgICAgICAgIHRoaXMucmVuZGVyQ2x1c3RlcnMoKTtcbiAgICAgICAgICAgICAgICAvLyBEZWxheWVkIHJlbW92YWwgb2YgdGhlIG1hcmtlcnMgb2YgdGhlIGZvcm1lciBncm91cHMuXG4gICAgICAgICAgICAgICAgcmVxdWVzdEFuaW1hdGlvbkZyYW1lKCgpID0+IGdyb3VwTWFya2Vycy5mb3JFYWNoKChtYXJrZXIpID0+IE1hcmtlclV0aWxzLnNldE1hcChtYXJrZXIsIG51bGwpKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBnb29nbGUubWFwcy5ldmVudC50cmlnZ2VyKHRoaXMsIE1hcmtlckNsdXN0ZXJlckV2ZW50cy5DTFVTVEVSSU5HX0VORCwgdGhpcyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgb25BZGQoKSB7XG4gICAgICAgIHRoaXMuaWRsZUxpc3RlbmVyID0gdGhpcy5nZXRNYXAoKS5hZGRMaXN0ZW5lcihcImlkbGVcIiwgdGhpcy5yZW5kZXIuYmluZCh0aGlzKSk7XG4gICAgICAgIHRoaXMucmVuZGVyKCk7XG4gICAgfVxuICAgIG9uUmVtb3ZlKCkge1xuICAgICAgICBnb29nbGUubWFwcy5ldmVudC5yZW1vdmVMaXN0ZW5lcih0aGlzLmlkbGVMaXN0ZW5lcik7XG4gICAgICAgIHRoaXMucmVzZXQoKTtcbiAgICB9XG4gICAgcmVzZXQoKSB7XG4gICAgICAgIHRoaXMubWFya2Vycy5mb3JFYWNoKChtYXJrZXIpID0+IE1hcmtlclV0aWxzLnNldE1hcChtYXJrZXIsIG51bGwpKTtcbiAgICAgICAgdGhpcy5jbHVzdGVycy5mb3JFYWNoKChjbHVzdGVyKSA9PiBjbHVzdGVyLmRlbGV0ZSgpKTtcbiAgICAgICAgdGhpcy5jbHVzdGVycyA9IFtdO1xuICAgIH1cbiAgICByZW5kZXJDbHVzdGVycygpIHtcbiAgICAgICAgLy8gR2VuZXJhdGUgc3RhdHMgdG8gcGFzcyB0byByZW5kZXJlcnMuXG4gICAgICAgIGNvbnN0IHN0YXRzID0gbmV3IENsdXN0ZXJTdGF0cyh0aGlzLm1hcmtlcnMsIHRoaXMuY2x1c3RlcnMpO1xuICAgICAgICBjb25zdCBtYXAgPSB0aGlzLmdldE1hcCgpO1xuICAgICAgICB0aGlzLmNsdXN0ZXJzLmZvckVhY2goKGNsdXN0ZXIpID0+IHtcbiAgICAgICAgICAgIGlmIChjbHVzdGVyLm1hcmtlcnMubGVuZ3RoID09PSAxKSB7XG4gICAgICAgICAgICAgICAgY2x1c3Rlci5tYXJrZXIgPSBjbHVzdGVyLm1hcmtlcnNbMF07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAvLyBHZW5lcmF0ZSB0aGUgbWFya2VyIHRvIHJlcHJlc2VudCB0aGUgZ3JvdXAuXG4gICAgICAgICAgICAgICAgY2x1c3Rlci5tYXJrZXIgPSB0aGlzLnJlbmRlcmVyLnJlbmRlcihjbHVzdGVyLCBzdGF0cywgbWFwKTtcbiAgICAgICAgICAgICAgICAvLyBNYWtlIHN1cmUgYWxsIGluZGl2aWR1YWwgbWFya2VycyBhcmUgcmVtb3ZlZCBmcm9tIHRoZSBtYXAuXG4gICAgICAgICAgICAgICAgY2x1c3Rlci5tYXJrZXJzLmZvckVhY2goKG1hcmtlcikgPT4gTWFya2VyVXRpbHMuc2V0TWFwKG1hcmtlciwgbnVsbCkpO1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLm9uQ2x1c3RlckNsaWNrKSB7XG4gICAgICAgICAgICAgICAgICAgIGNsdXN0ZXIubWFya2VyLmFkZExpc3RlbmVyKFwiY2xpY2tcIiwgXG4gICAgICAgICAgICAgICAgICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovXG4gICAgICAgICAgICAgICAgICAgIChldmVudCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgZ29vZ2xlLm1hcHMuZXZlbnQudHJpZ2dlcih0aGlzLCBNYXJrZXJDbHVzdGVyZXJFdmVudHMuQ0xVU1RFUl9DTElDSywgY2x1c3Rlcik7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9uQ2x1c3RlckNsaWNrKGV2ZW50LCBjbHVzdGVyLCBtYXApO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBNYXJrZXJVdGlscy5zZXRNYXAoY2x1c3Rlci5tYXJrZXIsIG1hcCk7XG4gICAgICAgIH0pO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgQWJzdHJhY3RBbGdvcml0aG0sIEFic3RyYWN0Vmlld3BvcnRBbGdvcml0aG0sIENsdXN0ZXIsIENsdXN0ZXJTdGF0cywgRGVmYXVsdFJlbmRlcmVyLCBHcmlkQWxnb3JpdGhtLCBNYXJrZXJDbHVzdGVyZXIsIE1hcmtlckNsdXN0ZXJlckV2ZW50cywgTWFya2VyVXRpbHMsIE5vb3BBbGdvcml0aG0sIFN1cGVyQ2x1c3RlckFsZ29yaXRobSwgU3VwZXJDbHVzdGVyVmlld3BvcnRBbGdvcml0aG0sIGRlZmF1bHRPbkNsdXN0ZXJDbGlja0hhbmRsZXIsIGRpc3RhbmNlQmV0d2VlblBvaW50cywgZXh0ZW5kQm91bmRzVG9QYWRkZWRWaWV3cG9ydCwgZXh0ZW5kUGl4ZWxCb3VuZHMsIGZpbHRlck1hcmtlcnNUb1BhZGRlZFZpZXdwb3J0LCBnZXRQYWRkZWRWaWV3cG9ydCwgbm9vcCwgcGl4ZWxCb3VuZHNUb0xhdExuZ0JvdW5kcyB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguZXNtLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@googlemaps/markerclusterer/dist/index.esm.js\n");

/***/ })

};
;