/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(user)/guide/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-analytics.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-email-capture.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-hero-section.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-secondary-cta.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-social-proof.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-value-proposition.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-analytics.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-email-capture.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-hero-section.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-secondary-cta.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-social-proof.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-value-proposition.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/(user)/guide/components/guide-analytics.tsx */ \"(app-pages-browser)/./app/[locale]/(user)/guide/components/guide-analytics.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/(user)/guide/components/guide-email-capture.tsx */ \"(app-pages-browser)/./app/[locale]/(user)/guide/components/guide-email-capture.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/(user)/guide/components/guide-hero-section.tsx */ \"(app-pages-browser)/./app/[locale]/(user)/guide/components/guide-hero-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/(user)/guide/components/guide-secondary-cta.tsx */ \"(app-pages-browser)/./app/[locale]/(user)/guide/components/guide-secondary-cta.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/(user)/guide/components/guide-social-proof.tsx */ \"(app-pages-browser)/./app/[locale]/(user)/guide/components/guide-social-proof.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/(user)/guide/components/guide-value-proposition.tsx */ \"(app-pages-browser)/./app/[locale]/(user)/guide/components/guide-value-proposition.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q19QUklWQVRFJTVDJTVDUHJvcGVydHklMjBQbGF6YSUyMERFViU1QyU1Q3Byb3BlcnR5LXBsYXphJTVDJTVDYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDKHVzZXIpJTVDJTVDZ3VpZGUlNUMlNUNjb21wb25lbnRzJTVDJTVDZ3VpZGUtYW5hbHl0aWNzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q19QUklWQVRFJTVDJTVDUHJvcGVydHklMjBQbGF6YSUyMERFViU1QyU1Q3Byb3BlcnR5LXBsYXphJTVDJTVDYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDKHVzZXIpJTVDJTVDZ3VpZGUlNUMlNUNjb21wb25lbnRzJTVDJTVDZ3VpZGUtZW1haWwtY2FwdHVyZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNfUFJJVkFURSU1QyU1Q1Byb3BlcnR5JTIwUGxhemElMjBERVYlNUMlNUNwcm9wZXJ0eS1wbGF6YSU1QyU1Q2FwcCU1QyU1QyU1QmxvY2FsZSU1RCU1QyU1Qyh1c2VyKSU1QyU1Q2d1aWRlJTVDJTVDY29tcG9uZW50cyU1QyU1Q2d1aWRlLWhlcm8tc2VjdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNfUFJJVkFURSU1QyU1Q1Byb3BlcnR5JTIwUGxhemElMjBERVYlNUMlNUNwcm9wZXJ0eS1wbGF6YSU1QyU1Q2FwcCU1QyU1QyU1QmxvY2FsZSU1RCU1QyU1Qyh1c2VyKSU1QyU1Q2d1aWRlJTVDJTVDY29tcG9uZW50cyU1QyU1Q2d1aWRlLXNlY29uZGFyeS1jdGEudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDX1BSSVZBVEUlNUMlNUNQcm9wZXJ0eSUyMFBsYXphJTIwREVWJTVDJTVDcHJvcGVydHktcGxhemElNUMlNUNhcHAlNUMlNUMlNUJsb2NhbGUlNUQlNUMlNUModXNlciklNUMlNUNndWlkZSU1QyU1Q2NvbXBvbmVudHMlNUMlNUNndWlkZS1zb2NpYWwtcHJvb2YudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDX1BSSVZBVEUlNUMlNUNQcm9wZXJ0eSUyMFBsYXphJTIwREVWJTVDJTVDcHJvcGVydHktcGxhemElNUMlNUNhcHAlNUMlNUMlNUJsb2NhbGUlNUQlNUMlNUModXNlciklNUMlNUNndWlkZSU1QyU1Q2NvbXBvbmVudHMlNUMlNUNndWlkZS12YWx1ZS1wcm9wb3NpdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNfUFJJVkFURSU1QyU1Q1Byb3BlcnR5JTIwUGxhemElMjBERVYlNUMlNUNwcm9wZXJ0eS1wbGF6YSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQtaW50bCU1QyU1Q2Rpc3QlNUMlNUNlc20lNUMlNUNuYXZpZ2F0aW9uJTVDJTVDc2hhcmVkJTVDJTVDQmFzZUxpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNfUFJJVkFURSU1QyU1Q1Byb3BlcnR5JTIwUGxhemElMjBERVYlNUMlNUNwcm9wZXJ0eS1wbGF6YSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQtaW50bCU1QyU1Q2Rpc3QlNUMlNUNlc20lNUMlNUNuYXZpZ2F0aW9uJTVDJTVDc2hhcmVkJTVDJTVDTGVnYWN5QmFzZUxpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE9BQWlMO0FBQ2pMO0FBQ0Esc1BBQXFMO0FBQ3JMO0FBQ0Esb1BBQW9MO0FBQ3BMO0FBQ0Esc1BBQXFMO0FBQ3JMO0FBQ0Esb1BBQW9MO0FBQ3BMO0FBQ0EsOFBBQXlMO0FBQ3pMO0FBQ0Esd1BBQXVMO0FBQ3ZMO0FBQ0Esb1FBQTZMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/ZjhmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxfUFJJVkFURVxcXFxQcm9wZXJ0eSBQbGF6YSBERVZcXFxccHJvcGVydHktcGxhemFcXFxcYXBwXFxcXFtsb2NhbGVdXFxcXCh1c2VyKVxcXFxndWlkZVxcXFxjb21wb25lbnRzXFxcXGd1aWRlLWFuYWx5dGljcy50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxfUFJJVkFURVxcXFxQcm9wZXJ0eSBQbGF6YSBERVZcXFxccHJvcGVydHktcGxhemFcXFxcYXBwXFxcXFtsb2NhbGVdXFxcXCh1c2VyKVxcXFxndWlkZVxcXFxjb21wb25lbnRzXFxcXGd1aWRlLWVtYWlsLWNhcHR1cmUudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcX1BSSVZBVEVcXFxcUHJvcGVydHkgUGxhemEgREVWXFxcXHByb3BlcnR5LXBsYXphXFxcXGFwcFxcXFxbbG9jYWxlXVxcXFwodXNlcilcXFxcZ3VpZGVcXFxcY29tcG9uZW50c1xcXFxndWlkZS1oZXJvLXNlY3Rpb24udHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcX1BSSVZBVEVcXFxcUHJvcGVydHkgUGxhemEgREVWXFxcXHByb3BlcnR5LXBsYXphXFxcXGFwcFxcXFxbbG9jYWxlXVxcXFwodXNlcilcXFxcZ3VpZGVcXFxcY29tcG9uZW50c1xcXFxndWlkZS1zZWNvbmRhcnktY3RhLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXF9QUklWQVRFXFxcXFByb3BlcnR5IFBsYXphIERFVlxcXFxwcm9wZXJ0eS1wbGF6YVxcXFxhcHBcXFxcW2xvY2FsZV1cXFxcKHVzZXIpXFxcXGd1aWRlXFxcXGNvbXBvbmVudHNcXFxcZ3VpZGUtc29jaWFsLXByb29mLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXF9QUklWQVRFXFxcXFByb3BlcnR5IFBsYXphIERFVlxcXFxwcm9wZXJ0eS1wbGF6YVxcXFxhcHBcXFxcW2xvY2FsZV1cXFxcKHVzZXIpXFxcXGd1aWRlXFxcXGNvbXBvbmVudHNcXFxcZ3VpZGUtdmFsdWUtcHJvcG9zaXRpb24udHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcX1BSSVZBVEVcXFxcUHJvcGVydHkgUGxhemEgREVWXFxcXHByb3BlcnR5LXBsYXphXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0LWludGxcXFxcZGlzdFxcXFxlc21cXFxcbmF2aWdhdGlvblxcXFxzaGFyZWRcXFxcQmFzZUxpbmsuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxfUFJJVkFURVxcXFxQcm9wZXJ0eSBQbGF6YSBERVZcXFxccHJvcGVydHktcGxhemFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHQtaW50bFxcXFxkaXN0XFxcXGVzbVxcXFxuYXZpZ2F0aW9uXFxcXHNoYXJlZFxcXFxMZWdhY3lCYXNlTGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-analytics.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-email-capture.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-hero-section.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-secondary-cta.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-social-proof.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(user)%5C%5Cguide%5C%5Ccomponents%5C%5Cguide-value-proposition.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20DEV%5C%5Cproperty-plaza%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cnavigation%5C%5Cshared%5C%5CLegacyBaseLink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/[locale]/(user)/guide/components/guide-social-proof.tsx":
/*!*********************************************************************!*\
  !*** ./app/[locale]/(user)/guide/components/guide-social-proof.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GuideSocialProof; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction GuideSocialProof() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"guide\");\n    const testimonials = [\n        {\n            name: \"Sarah M.\",\n            role: \"Digital Nomad from Netherlands\",\n            avatar: \"https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2\",\n            quote: \"Finally found a trustworthy source for Bali housing. The guide saved me from a €2000 scam! The zoning section alone is worth gold.\",\n            rating: 5\n        },\n        {\n            name: \"Marcus T.\",\n            role: \"Remote Developer from Germany\",\n            avatar: \"https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2\",\n            quote: \"Wish I had this guide before my first move. Would have saved months of stress and bad landlords. Now I know exactly what to look for.\",\n            rating: 5\n        },\n        {\n            name: \"Lisa K.\",\n            role: \"Entrepreneur from Australia\",\n            avatar: \"https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2\",\n            quote: \"The zoning section alone is worth gold. No more confusion about what I can actually rent. Property Plaza's team really knows their stuff.\",\n            rating: 5\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-6\",\n                                children: \"Trusted by Expats Worldwide\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 mb-8\",\n                                children: \"Join thousands of expats who've successfully found their perfect home in Bali using our insider knowledge and verified listings.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-seekers-primary mb-1\",\n                                                children: \"5,000+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Guides Downloaded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-seekers-primary mb-1\",\n                                                children: \"2,500+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Happy Expats\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-seekers-primary mb-1\",\n                                                children: \"98%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Success Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-seekers-primary mb-1\",\n                                                children: \"4.9/5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Average Rating\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 mb-3\",\n                                        children: [\n                                            ...Array(testimonial.rating)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-3 w-3 fill-yellow-400 text-yellow-400\"\n                                            }, i, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                        className: \"text-gray-700 mb-4 text-sm leading-relaxed\",\n                                        children: [\n                                            '\"',\n                                            testimonial.quote,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-8 h-8 rounded-full overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: testimonial.avatar,\n                                                    alt: \"\".concat(testimonial.name, \" avatar\"),\n                                                    fill: true,\n                                                    className: \"object-cover\",\n                                                    sizes: \"32px\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900 text-sm\",\n                                                        children: testimonial.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: testimonial.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-social-proof.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(GuideSocialProof, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations\n    ];\n});\n_c = GuideSocialProof;\nvar _c;\n$RefreshReg$(_c, \"GuideSocialProof\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/(user)/guide/components/guide-social-proof.tsx\n"));

/***/ })

});