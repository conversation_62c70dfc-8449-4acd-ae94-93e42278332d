"use client"

import { useEffect, useState } from "react"
import propertyDetailUtils, { IMAGE_DETAIL_BUTTON_ID, IMAGE_DIALOG_BUTTON_ID, OPEN_IMAGE_DIALOG_STATUS, UPDATE_STATUS_IMAGE_CAROUSEL_DETAIL } from "../utils/use-image-gallery"
import { useTranslations } from "next-intl"
import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper"
import { Button } from "@/components/ui/button"
import { ImageIcon } from "lucide-react"
import DialogHeaderWrapper from "@/components/dialog-wrapper/dialog-header-wrapper"
import Image from "next/image"
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area"
import { useUserStore } from "@/stores/user.store"
import { imagePlaceholder } from "@/lib/constanta/image-placeholder"
import { cn } from "@/lib/utils"
import { plansUrl } from "@/lib/constanta/route"
import Link from "next/link"
import { packages } from "@/core/domain/subscription/subscription"

export default function ImageGalleryDialog({ imagesUrl }: { imagesUrl: string[] }) {

  const t = useTranslations("seeker")
  const [open, setOpen] = useState(false)
  const [tempClose, setTempClose] = useState(false)
  const { seekers } = useUserStore()
  const { handleShareActiveImageCarousel } = propertyDetailUtils()


  useEffect(() => {
    const handleSetTempClose = (e: Event) => {
      const event = e as CustomEvent<{ isOpen?: boolean }>
      setTempClose(event.detail.isOpen || false)
    }
    window.addEventListener(UPDATE_STATUS_IMAGE_CAROUSEL_DETAIL, handleSetTempClose)
    return () => {
      window.removeEventListener(UPDATE_STATUS_IMAGE_CAROUSEL_DETAIL, handleSetTempClose)
    }
  }, [])

  const handleOpenDetailCarousel = () => {
    const imageDetailCarouselButton = document.getElementById(IMAGE_DETAIL_BUTTON_ID)
    imageDetailCarouselButton?.click()
  }
  return <DialogWrapper
    dialogClassName={"!w-[95vw] md:max-h-[95vh] md:!min-w-xl h-fit max-w-7xl max-h-screen overflow-hidden"}
    open={tempClose ? false : open}
    setOpen={setOpen}
    openTrigger={
      <Button variant={"ghost"} id={IMAGE_DIALOG_BUTTON_ID} className="absolute bottom-4 right-4 z-10 bg-white text-seekers-text-light font-medium gap-3">
        <ImageIcon className="!w-6 !h-6" />
        {t('listing.detail.images.showAllImages')}
      </Button>
    } >
    <DialogHeaderWrapper>
      <h2 className="text-base font-bold text-seekers-text text-center">{t('listing.detail.images.title')}</h2>
    </DialogHeaderWrapper>
    <ScrollArea className={cn("max-h-full h-[80vh]", seekers.accounts.membership == packages.free ? "overflow-hidden" : "")}>
      <div className="px-4">
        <div className="grid md:grid-cols-3 gap-3">
          {imagesUrl.map((item, idx) => (
            <div key={idx} className="relative rounded-lg aspect-[4/3]">
              <div className="relative w-full h-full overflow-hidden rounded-lg isolate">
                <div className="absolute inset-0 z-10 pointer-events-none watermark-overlay" />
                <Image
                  src={item}
                  alt=""
                  loading="lazy"
                  fill
                  className="object-cover hover:scale-110 transition-transform duration-300"
                  style={{ objectFit: "cover" }}
                  onClick={() => {
                    handleOpenDetailCarousel()
                    handleShareActiveImageCarousel(idx)
                  }}
                />
              </div>
            </div>
          ))}
        </div>
        {seekers.accounts.membership == packages.free &&
          <div className="mt-3 relative">
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-t from-stone-950 via-stone-950/80 to-stone-950/0 z-10"></div>
            <div className="absolute top-1/4 left-1/2 -translate-x-1/2 z-20 text-white flex flex-col items-center">
              <p className="max-w-md text-center text-white">{t('misc.subscibePropgram.detailPage.description')}</p>
              <Button asChild variant={"link"} size={"sm"} className="p-0 h-fit w-fit mx-auto text-white underline">
                <Link href={plansUrl}>{t('cta.subscribe')}</Link>
              </Button>
            </div>
            <div className="grid md:grid-cols-3 gap-3">
              {[0, 1, 2, 3, 4, 5].map((item, idx) => (
                <div key={idx} className="relative rounded-lg aspect-[4/3]">
                  <div className="relative w-full h-full overflow-hidden rounded-lg isolate">
                    <div className="absolute inset-0 z-10 pointer-events-none watermark-overlay" />
                    <Image
                      src={imagePlaceholder}
                      alt=""
                      loading="lazy"
                      fill
                      className="object-cover hover:scale-110 transition-transform duration-300 blur-md"
                      style={{ objectFit: "cover" }}

                      onClick={() => {
                        handleOpenDetailCarousel()
                        handleShareActiveImageCarousel(idx)
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        }
      </div>
      <ScrollBar orientation="vertical" className="!w-1.5" />
    </ScrollArea>
  </DialogWrapper>
}