import { Button } from "@/components/ui/button";
import { ListingCategory } from "@/core/domain/listing/listing-seekers";
import { filterTitles } from "@/lib/constanta/constant";
import { searchUrl } from "@/lib/constanta/route";
import Link from "next/link";
import { ComponentProps } from "react";

export interface CategoryItemProps extends ComponentProps<"div"> {
  title: string,
  icon: React.ReactNode,
  slug: string,
  value: ListingCategory
}
export default function CategoryItem({ icon, title, slug, value }: CategoryItemProps) {
  return <Button variant={"ghost"} className="w-full !h-fit" asChild>
    <Link href={searchUrl + "/all?" + filterTitles.type + "=" + value}>
      <div className="flex flex-col items-center gap-1 text-seekers-text">
        {icon}
        <p className="font-medium text-xs">{title}</p>
      </div>
    </Link>
  </Button >
}