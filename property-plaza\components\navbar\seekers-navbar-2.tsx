"use client"
import useSeekersSearch from "@/hooks/use-seekers-search";
import MainContentLayout from "../seekers-content-layout/main-content-layout";
import Image from "next/image";
import Logo from "@/public/property-seekers-main-logo.png"
import { Button } from "../ui/button";
import { Menu, Search, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion"
import { useSeekerSearchStore } from "@/stores/seeker-search.store";
import { cn } from "@/lib/utils";
import SeekersRightNavbar from "./seekers-right-navbar-2";
import LocationSearch from "./location-search/location-search-form";
import SeekerSearchDialog from "./seeker-search-dialog";
import TypeSearch from "./category-search/category-search-form";
import { Link } from "@/lib/locale/navigations";

export default function SeekersNavbar({ localeId = "EN", currency_ = "EUR" }: { localeId?: string, currency_?: string }) {
  const { handleSearch } = useSeekersSearch()
  const [open, setOpen] = useState(false) //for handling mobile navbar
  const mobileRef = useRef<HTMLDivElement | null>(null)
  const navbarRef = useRef<HTMLDivElement | null>(null)
  const { isOpen, setIsOpen, categoryInputFocused, locationInputFocused } = useSeekerSearchStore(state => state)
  useEffect(() => {
    const handleScrollY = () => {
      setOpen(false)
      if (window.scrollY < 200) {
        setIsOpen(true)
        return
      } else if (window.scrollY > 200 && (categoryInputFocused || locationInputFocused)) {
        setIsOpen(true)
      } else {
        setIsOpen(false)
      }
    }
    window.addEventListener("scroll", handleScrollY)
    return () => {
      window.removeEventListener("scroll", handleScrollY)
    }
  }, [categoryInputFocused, isOpen, locationInputFocused, setIsOpen])
  useEffect(() => {
    if (categoryInputFocused || locationInputFocused) {
      setIsOpen(true)
      return
    }
  }, [categoryInputFocused, locationInputFocused, setIsOpen])
  return <AnimatePresence>
    <nav ref={navbarRef} className="w-full max-xl:space-y-4 border-b shadow-sm shadow-neutral-600/20 bg-white md:h-[90px] lg:h-[114px]">
      <MainContentLayout className="!h-full relative py-4 max-lg:space-y-4 xl:py-6 space-y-8 ">
        <div className="w-full flex justify-between items-center flex-wrap gap-y-6">
          <Link href={"/"}>
            <Image src={Logo} alt="Property-Plaza" width={164} height={24} />
          </Link>
          <motion.div
            className="flex gap-2 rounded-full p-2 border border-seekers-text-lighter shadow-md items-center max-lg:hidden pl-4"
            initial={{ opacity: 1, width: "60%" }}
            animate={{
              width: !isOpen ? "30%" : "60%",
            }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex flex-grow items-center overflow-hidden divide-x-2 divide-seekers-text-lighter">
              <div className="flex-grow min-w-[49%] max-w-[50%] pr-8">
                <LocationSearch />
              </div>
              <div className="flex-grow min-w-[49%] max-w-[50%] pl-8">
                <TypeSearch />
              </div>
            </div>
            <motion.div
              initial={{
                height: 48,
                width: 48
              }}
              animate={{
                height: !isOpen ? 36 : 48,
                width: !isOpen ? 36 : 48
              }}
              transition={{ duration: 0.3 }}

            >
              <Button variant={"default-seekers"} onClick={() => handleSearch()} className="rounded-full w-full h-full !aspect-square" size={"icon"}>
                <Search className="!w-5 !h-5" strokeWidth={3} />
              </Button>
            </motion.div>
          </motion.div>
          <div className="lg:hidden max-sm:w-full md:max-lg:w-[50%] max-sm:order-last flex gap-2">
            <SeekerSearchDialog />
          </div>
          <div className="md:hidden flex gap-1 w-[164px] justify-end">
            <Button variant={"ghost"} className="px-0 pl-4" onClick={() => setOpen(prev => !prev)}>
              <Menu className="!h-6 !w-6" />
            </Button>
          </div>
          <div className="max-md:hidden flex gap-2 items-center w-fit justify-end min-w-[136px]">
            <SeekersRightNavbar currency_={currency_} localeId={localeId} />
          </div>

        </div>
      </MainContentLayout>
      <div className={cn(open ? "fixed w-screen h-full bg-seekers-text/30 top-0 left-0 -z-10 !mt-0" : "hidden")}>
      </div>
      <div ref={mobileRef} className={`absolute top-12 z-30 bg-background left-0 w-full flex gap-2 items-center justify-end  ${open ? "h-fit  py-4 px-4" : "h-0"} overflow-hidden transition-all ease-linear duration-75 transform`}>
        <SeekersRightNavbar currency_={currency_} localeId={localeId} />
      </div>
    </nav>
  </AnimatePresence>
}


