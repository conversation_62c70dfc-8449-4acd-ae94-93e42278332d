import { ArrowR<PERSON> } from "lucide-react";
import Link from "next/link";

export default function PostRecommendation({ posts, title }: { title: string, posts: { title: string, url: string }[] }) {
  return <section className="p-4 rounded-lg bg-seekers-primary/10 space-y-4 ">
    <p className="font-semibold text-lg">{title}</p>
    {posts.map((item, idx) => <Link key={idx} className="text-seekers-primary flex gap-2.5 group" href={item.url}>
      <ArrowRight className="w-3 group-hover:translate-x-2 transition-transform duration-100 ease-linear" />
      {item.title}
    </Link >
    )}
  </section>
}