import { ListingContractType } from "@/core/domain/listing/listing"
import { ListingListSeekers } from "@/core/domain/listing/listing-seekers"
import { seekersPriceHelper } from "@/lib/listing-price-formatter"
import { uppercaseFirstLetter } from "@/lib/utils"
import ListingWrapper from "./listing-wrapper"
import FormatPrice from "./format-price"
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api"

export async function ListingPrice({ listing, locale = "en", currency = "EUR", conversions }: { listing: ListingListSeekers, locale: string, currency: string, conversions: { [key: string]: number } }) {

  const { startWord, formattedPrice, suffix } = await seekersPriceHelper(
    listing.price,
    listing.availability.type as ListingContractType,
    listing.availability.minDuration || undefined,
    listing.availability.maxDuration || undefined
  )
  return <ListingWrapper listing={listing} >
    <p className=" text-base text-seekers-text font-medium ">
      {/* This check if there's minimum duration, so it will become rent */}

      <span className="text-sm font-medium text-seekers-text-lighter">
        {uppercaseFirstLetter(startWord)}
      </span>
      {" "}
      <FormatPrice
        price={formattedPrice}
        currency_={currency}
        locale_={locale}
        conversion={conversions}
      />
      {" "}
      <span className="text-xs text-seekers-text-lighter">{suffix}</span>

    </p>
  </ListingWrapper>
}