import { PortableText } from "@portabletext/react";
import Image from "next/image";
import { type PortableTextBlock } from "next-sanity";
import moment from "moment";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Blog } from "@/core/services/sanity/types";
import { useTranslations } from "next-intl";
import AboutUsImage from "@/public/about-us-image.webp"
import PostRecommendation from "./post-recommendation";
import PropertyRecommendation from "./property-recommendation";

export default function BlogContent({ blogContent, conversions }: { blogContent: Blog, conversions: { [key: string]: number } }) {
  const t = useTranslations("seeker")
  return <MainContentLayout className="xl:max-w-screen-lg mx-auto min-h-screen space-y-6 py-6 max-sm:pt-0">
    <>
      <div className="relative w-full aspect-video rounded-xl overflow-hidden max-sm:rounded-none">
        <Image
          style={{ objectFit: "cover" }}
          src={blogContent.mainImage?.asset?.url || AboutUsImage}
          alt=""
          fill
        />
      </div>
      <div className="space-y-3 max-sm:px-4">
        <h1 className="text-3xl font-bold text-seekers-text">{blogContent.title}</h1>
        <div className="flex gap-2">
          <div className="aspect-square h-full min-w-10 relative rounded-full overflow-hidden">
            <Image
              src={blogContent.author?.image?.asset?.url || AboutUsImage} fill alt=""
              style={{ objectFit: "cover" }}
            />
          </div>
          <div>
            <p className="text-seekers-text">{blogContent.author.name || t('blog.author.defaultName')}</p>
            <p className="text-seekers-text-light">{moment(blogContent.publishedAt).format("MMM DD, YYYY")}</p>
          </div>
        </div>
      </div>
      <section className="space-y-3 text-seekers-text text-base max-sm:px-4">
        <PortableText value={blogContent.body as PortableTextBlock[]}
          components={{
            block: {
              h1: ({ children }) => <h2 className="text-2xl text-seekers-text">{children}</h2>,
              h2: ({ children }) => <h3 className="text-xl">{children}</h3>,
              h3: ({ children }) => <h4 className="text-lg">{children}</h4>,
              h4: ({ children }) => <h5 className="">{children}</h5>,
              normal: ({ children }) => <p className=" leading-relaxed">{children}</p>,
            },

            list: {
              number: ({ children }) => <ol className="list-decimal list-inside">{children}</ol>,
              bullet: ({ children }) => <ul className="list-disc pl-4">{children}</ul>
            },
            types: {
              'post-recommendation': ({ value }) => {
                return <PostRecommendation title={value.title} posts={value.posts} />
              }
              ,
              'property-recommendation': ({ value }) => {
                const propertyCodes = value.multiplePropertyId.split(" ")
                return <PropertyRecommendation propertyIds={propertyCodes} conversions={conversions} />
              },
            }

          }} />
      </section>
    </>
  </MainContentLayout>
}