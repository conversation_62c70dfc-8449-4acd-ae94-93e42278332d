"use client"

import { useGetLocationSuggestion } from "@/core/applications/queries/listing/use-get-location-suggestion"
import { useDebounce } from "@/hooks/use-debounce"
import useSeekersSearch from "@/hooks/use-seekers-search"
import { useSeekerSearchStore } from "@/stores/seeker-search.store"
import { ArrowLeft, Check, ChevronRight, MapPin } from "lucide-react"
import { useTranslations } from "next-intl"
import { useEffect } from "react"
import LocationIconFormatter from "./location-icon-formatter"

export default function LocationSearchContent({ showContent }: { showContent?: (val: boolean) => void }) {
  const t = useTranslations("seeker")
  const { query } = useSeekerSearchStore(state => state)
  const debounceValue = useDebounce(query, 500)
  const locationSuggestion = useGetLocationSuggestion({ search: debounceValue })

  const {
    handleSetQuery, seekersSearch, banjars,
    showBanjars, selectedLocation,
    handleSelectLocation, handleBackToLocations, handleSetBanjar, filteredLocations, getMatchingBanjars } = useSeekersSearch()

  useEffect(() => {
    if (locationSuggestion.isPending) {
      return showContent?.(true)
    }
    if (filteredLocations.length <= 0 && locationSuggestion.data?.data?.length! <= 0) {
      showContent?.(false)
    } else {
      showContent?.(true)
    }

  }, [
    query.length,
    filteredLocations.length,
    locationSuggestion.data?.data?.length,
    locationSuggestion.isPending,
    showContent
  ])
  return <>
    {!showBanjars ? <>
      {/* Show main region */}
      {filteredLocations.length > 0 &&
        <div>
          <h2>{t('misc.region')}</h2>
          {filteredLocations.map(location => {
            const cleanLocationName = location.name.replace(", Bali", "")
            const hasBanjars = banjars[location.value as keyof typeof banjars]?.length > 0

            return <div key={location.value}>
              <button
                className="w-full flex items-center justify-between p-3 hover:bg-gray-100 transition-colors rounded-lg"
                onClick={e => {

                  e.preventDefault()
                  e.stopPropagation()
                  if (hasBanjars) {
                    handleSelectLocation(location.value)
                  } else {
                    handleSetQuery(location.value)
                  }
                }
                }
              >
                <div className="flex items-center gap-3">
                  <LocationIconFormatter locationName={location.value} />
                  <div className="text-left">
                    <div className="font-medium">{cleanLocationName}</div>
                    <div className="text-sm text-gray-500">{location.description}</div>
                  </div>
                </div>
                {hasBanjars && <ChevronRight className="h-4 w-4 text-gray-400" />}
              </button>
            </div>
          })
          }
        </div>
      }
    </>
      :
      <>
        {/* Show banjar based on region */}
        <div className="mt-4">
          <div className="flex items-center gap-3 mb-4  px-3 max-sm:px-0">
            <button onClick={e => {
              e.preventDefault()
              e.stopPropagation()
              handleBackToLocations()
            }} className="text-seekers-text-light hover:text-seekers-text">
              <ArrowLeft className="h-4 w-4" />
            </button>
            <span className="font-medium capitalize">{selectedLocation}</span>
          </div>
          <div className="grid grid-cols-2 gap-4 px-3 max-sm:px-0">
            {banjars[selectedLocation as keyof typeof banjars].map((banjar) => (
              <div key={banjar} className="relative ">
                <button
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleSetBanjar(banjar)
                  }}
                  className={`w-full border border-gray-200 rounded-full py-3 px-4 flex items-center gap-3
                                      ${seekersSearch.query.includes(banjar) ? "bg-gray-50" : "bg-white"}`}
                >
                  <div
                    className={`w-4 h-4 rounded-full border flex items-center justify-center
                                      ${seekersSearch.query.includes(banjar) ? "border-seekers-primary bg-seekers-primary" : "border-gray-300"}`}
                  >
                    {
                      seekersSearch.query.includes(banjar) && <Check className="h-3 w-3 text-white" />
                    }
                  </div>
                  <span className="text-sm text-seekers-text-light">{banjar}</span>
                </button>
              </div>
            ))}
          </div>
        </div>
      </>
    }
    {query.length >= 3 && !showBanjars &&
      (
        filteredLocations.some((location) => getMatchingBanjars(location.value).length > 0)
        || (locationSuggestion.data?.data?.length || 0) > 0
      ) && (
        <>
          <div className="text-xs text-gray-500 font-medium mt-4 mb-2 px-3">{t('misc.areas')}</div>
          <div>
            {filteredLocations.map((location) => {
              const matchingBanjars = getMatchingBanjars(location.value)
              if (matchingBanjars.length === 0) return null

              return matchingBanjars.map((banjar) => (
                <button
                  key={`${location.name}-${banjar}`}
                  className="w-full flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors rounded-lg"
                  onClick={() => {
                    handleSetBanjar(banjar, true)
                  }}
                >
                  <div className="w-8 h-8 flex items-center justify-center">
                    <MapPin className="w-4 h-4 text-seekers-primary" />
                  </div>
                  <div className="text-left">
                    <div className="font-medium">{banjar}</div>
                    <div className="text-sm text-gray-500">{location.name}</div>
                  </div>
                </button>
              ))
            })}
            {locationSuggestion.data?.data?.map((item, idx) => <button
              key={idx}
              className="w-full flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors rounded-lg"
              onClick={() => {
                handleSetBanjar(item)
              }}
            >
              <div className="w-8 h-8 flex items-center justify-center">
                <MapPin className="w-4 h-4 text-seekers-primary" />
              </div>
              <div className="text-left">
                <div className="font-medium">{item}</div>
              </div>
            </button>)}
          </div>
        </>
      )}
  </>
}