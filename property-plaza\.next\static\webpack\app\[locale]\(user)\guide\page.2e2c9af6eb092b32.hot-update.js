"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(user)/guide/page",{

/***/ "(app-pages-browser)/./app/[locale]/(user)/guide/components/guide-hero-section.tsx":
/*!*********************************************************************!*\
  !*** ./app/[locale]/(user)/guide/components/guide-hero-section.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GuideHeroSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Download_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Download!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDown_Download_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,Download!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction GuideHeroSection() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)(\"guide\");\n    const scrollToEmailForm = ()=>{\n        const emailSection = document.getElementById(\"email-capture-section\");\n        emailSection === null || emailSection === void 0 ? void 0 : emailSection.scrollIntoView({\n            behavior: \"smooth\"\n        });\n        // Track CTA click\n        if ( true && window.gtag) {\n            window.gtag(\"event\", \"guide_download_cta_click\", {\n                event_category: \"Lead Magnet\",\n                event_label: \"Hero CTA\",\n                value: 1\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden pt-[140px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"https://images.pexels.com/photos/2166553/pexels-photo-2166553.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=1\",\n                        alt: \"Beautiful Bali villa with rice field view\",\n                        fill: true,\n                        className: \"object-cover\",\n                        priority: true,\n                        sizes: \"100vw\",\n                        quality: 85,\n                        placeholder: \"blur\",\n                        blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/40\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-8 lg:gap-12 items-center min-h-screen py-20 lg:py-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 lg:space-y-8 order-2 lg:order-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 lg:space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight\",\n                                            children: [\n                                                t(\"hero.title\"),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-400\",\n                                                    children: t(\"hero.titleHighlight\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg sm:text-xl lg:text-2xl text-gray-200 leading-relaxed max-w-2xl mx-auto lg:mx-0\",\n                                            children: t(\"hero.subtitle\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            onClick: scrollToEmailForm,\n                                            size: \"lg\",\n                                            className: \"bg-teal-600 hover:bg-teal-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, this),\n                                                t(\"hero.cta.primary\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"outline\",\n                                            onClick: scrollToEmailForm,\n                                            size: \"lg\",\n                                            className: \"border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 text-lg font-semibold rounded-lg transition-all duration-300 bg-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this),\n                                                t(\"hero.cta.secondary\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: t(\"hero.trustIndicators.free\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: t(\"hero.trustIndicators.noSpam\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: t(\"hero.trustIndicators.instant\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center lg:justify-end order-1 lg:order-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-72 sm:w-80 h-80 sm:h-96 bg-white rounded-2xl shadow-2xl p-3 sm:p-4 transform rotate-3 hover:rotate-0 transition-transform duration-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-br from-seekers-primary-lightest to-neutral-lightest rounded-xl flex flex-col items-center justify-center p-4 sm:p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 sm:w-16 h-12 sm:h-16 bg-seekers-primary rounded-full flex items-center justify-center mb-3 sm:mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-6 sm:h-8 w-6 sm:w-8 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-gray-800 font-bold text-base sm:text-lg text-center mb-2\",\n                                                    children: \"Bali Housing Guide 2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-xs sm:text-sm text-center mb-3 sm:mb-4 px-2\",\n                                                    children: \"Everything you need to know about finding safe, legal housing in Bali\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-24 sm:h-32 bg-white rounded-lg shadow-inner flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-xs\",\n                                                        children: \"PDF Preview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 sm:-top-4 -right-2 sm:-right-4 w-10 sm:w-12 h-10 sm:h-12 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg animate-bounce\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-bold\",\n                                            children: \"FREE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_Download_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-6 w-6 text-white/70\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\guide\\\\components\\\\guide-hero-section.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n_s(GuideHeroSection, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations\n    ];\n});\n_c = GuideHeroSection;\nvar _c;\n$RefreshReg$(_c, \"GuideHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/(user)/guide/components/guide-hero-section.tsx\n"));

/***/ })

});