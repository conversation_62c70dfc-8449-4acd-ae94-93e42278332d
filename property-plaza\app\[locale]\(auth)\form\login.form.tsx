"use client"
import { zodResolver } from "@hookform/resolvers/zod"
import { Form } from '@/components/ui/form'
import { z } from "zod"
import { useForm } from 'react-hook-form'
import DefaultInput from "@/components/input-form/default-input";
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { useLoginOwnerFormSchema } from "./use-login-form.schema"
import { useTranslations } from "next-intl"
import { useLogin } from "@/core/applications/mutations/auth/use-login"

import { LoginDto } from "@/core/infrastructures/auth/dto"

import { checkIfEmail } from "@/lib/utils"
import libPhoneNumber from "google-libphonenumber"
import { resetPassword, signUpUrl } from "@/lib/constanta/route"
import PasswordInput from "@/components/input-form/password-input"
import { useToast } from "@/hooks/use-toast"

const phoneUtil = libPhoneNumber.PhoneNumberUtil.getInstance()

interface LoginFormProps {
  isDialog?: boolean
  onSuccess?: () => void,
  children?: React.ReactNode,
  onResetPassword?: () => void,
  loginType?: "seekers" | "owner" | "middleman",
  hasSignUpRedirect?: boolean
}

export function LoginForm({ isDialog, onSuccess, children, onResetPassword, loginType = "owner", hasSignUpRedirect = true }: LoginFormProps) {
  const t = useTranslations("owner")
  const formSchema = useLoginOwnerFormSchema()
  const useLoginMutation = useLogin(loginType)
  type formSchemaType = z.infer<typeof formSchema>
  const { toast } = useToast()

  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      contact: "",
      password: ""
    },
  })
  async function onSubmit(values: z.infer<typeof formSchema>) {
    const isEmail = checkIfEmail(values.contact.replaceAll(/\s+/g, ""))
    const data: LoginDto = {
      username: values.contact.trim(),
      password: values.password,
      login_with: isEmail ? "DEFAULT" : "PHONE_NUMBER"
    }
    if (!isEmail) {
      const number = phoneUtil.parse(values.contact)
      const countryCode = ("+" + number.getCountryCode()) || "0"
      data.username = (number.getNationalNumber()?.toString() || "")
    }
    try {
      await useLoginMutation.mutateAsync(data)
      onSuccess?.()
    } catch (e: any) {
      toast({
        title: t("error.failedLogin.title"),
        description: e?.response?.data.message || "",
        variant: "destructive"
      })
    }
  }
  return <div className="w-full space-y-6">
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="!mb-12">
          <h1 className="text-2xl font-semibold text-center">{t("form.login.title")}</h1>
          <p className="text-center text-muted-foreground">{t("form.login.description")}</p>
        </div>
        <div className="space-y-4">
          <DefaultInput
            form={form}
            name="contact"
            variant="float"
            type="text"
            label={t("form.label.emailOrPhone")}
            placeholder={""}
            labelClassName="text-xs text-seekers-text-light font-normal"

            inputProps={{
              required: true,
              onKeyDown: (e) => {
                if (e.key === ' ' || e.key === "Unidentified") {
                  e.preventDefault()
                }
              },
            }}
          />
          <PasswordInput
            form={form}
            name="password"
            variant="float"
            label={t("form.label.password")}
            placeholder={""}
            labelClassName="text-xs text-seekers-text-light font-normal"

            inputProps={{
              required: true
            }}
          />

          <div className="text-xs text-neutral space-x-1 !mt-1">
            <span>{t("form.utility.forgotField", { field: t("form.field.password") })}</span>
            {
              onResetPassword ?
                <>
                  <button type="button" className="text-primary font-medium" onClick={onResetPassword}>
                    {t("form.utility.resetField", { field: t("form.field.password") })}
                  </button>
                </>
                :
                <Link href={resetPassword} className="text-primary font-medium">{t("form.utility.resetField", { field: t("form.field.password") })}</Link>
            }
          </div>
        </div>
        <Button className="w-full" loading={useLoginMutation.isPending}>
          {t("cta.login")}
        </Button>
        {!isDialog && hasSignUpRedirect
          &&
          <div className="text-xs text-neutral space-x-1 !mt-2 text-center">
            <span>{t("auth.misc.createAccount")}</span>
            <Link href={signUpUrl} className="text-primary font-medium">{t("cta.createAccount")}</Link>
          </div>
        }
      </form>
    </Form>
    {children}
  </div>
}