"use client"
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Carousel, CarouselContent, CarouselDots, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { imagePlaceholder } from "@/lib/constanta/image-placeholder";
import { X } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import propertyDetailUtils, { UPDATE_ACTIVE_IMAGE_CAROUSEL_ID_EVENT_NAME } from "../utils/use-image-gallery";
import SubscribeBanner from "@/components/subscribe/subscribe-banner";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { plansUrl } from "@/lib/constanta/route";

export default function ImageDetailCarousel({ imagesUrl, open, setOpen, isSubscribe }: {
  imagesUrl: string[], open: boolean, setOpen: (val: boolean) => void, isSubscribe?: boolean
}) {
  const t = useTranslations("seeker")
  const [selectedIdx, setSelectedIdx] = useState(0)
  const { handleOpenStatusImageDetailCarousel } = propertyDetailUtils()
  useEffect(() => {
    const handleSelectedIdx = (val: Event) => {
      const event = val as CustomEvent<{ activeIndex: number }>
      setSelectedIdx(event.detail.activeIndex)
    }
    window.addEventListener(UPDATE_ACTIVE_IMAGE_CAROUSEL_ID_EVENT_NAME, handleSelectedIdx)
    return () => {
      window.removeEventListener(UPDATE_ACTIVE_IMAGE_CAROUSEL_ID_EVENT_NAME, handleSelectedIdx)
    }
  }, [])
  useEffect(() => {
    const container = document.getElementsByTagName("body")[0]
    if (open) {
      handleOpenStatusImageDetailCarousel(true)
      container.style.overflow = "hidden"
    } else {
      handleOpenStatusImageDetailCarousel(false)
      container.style.overflow = "auto"
    }
  }, [open, handleOpenStatusImageDetailCarousel])

  return !open
    ? <></>
    : <div id="image-carousel-container" className="!mt-0 fixed  w-screen h-screen top-0 left-0 bg-black z-[60] flex flex-col justify-center isolate items-center">
      <Button variant={"ghost"} size={"icon"} className="text-white absolute max-sm:top-2 max-sm:right-2 top-4 right-4 z-[60]" onClick={() => setOpen(false)}>
        <X className="xl:!w-6 xl:!h-6" />
      </Button>{
        !isSubscribe ? <>
          <SubscribeBanner className="z-[60] top-12 w-full" />
        </>

          :
          <></>
      }
      <Carousel
        opts={{ loop: isSubscribe, startIndex: selectedIdx }}
        className={"group isolate w-full h-full  relative  overflow-hidden"}

      >
        <CarouselContent className="absolute top-0 left-0 w-full h-full ml-0 -z-20">
          {imagesUrl.map((item, idx) => <CarouselItem key={idx} className="relative">
            <div className="absolute max-sm:right-24 max-sm:top-[64%] bottom-8 right-1/4">
              <div className="inset-0 z-10 max-sm:w-24 max-sm:h-9 pointer-events-none watermark-overlay" />
            </div>
            <Image
              src={item}
              alt=""
              fill
              sizes="300px"
              priority
              blurDataURL={imagePlaceholder}
              placeholder="blur"
              style={{
                objectFit: "contain"
              }}
            />
          </CarouselItem>
          )}
          {!isSubscribe &&
            <CarouselItem className="flex flex-col justify-center items-center relative">
              <Image
                src={imagePlaceholder}
                alt=""
                fill
                sizes="300px"
                priority
                blurDataURL={imagePlaceholder}
                placeholder="blur"
                className="-z-10 blur-sm brightness-50 grayscale-50"
                style={{
                  objectFit: "contain"
                }}
              />
              <p className="max-w-48 text-center text-white">{t('misc.subscibePropgram.detailPage.description')}</p>
              <Button asChild variant={"link"} size={"sm"} className="p-0 h-fit w-fit text-white underline">
                <Link href={plansUrl}>{t('cta.subscribe')}</Link>
              </Button>
            </CarouselItem>
          }
        </CarouselContent>

        {imagesUrl.length <= 1 ? <></> :
          <div className="flex absolute top-1/2 -translate-y-1/2 left-0 w-full justify-between px-3">
            <CarouselPrevious
              iconClassName="xl:!w-6 xl:!h-6"
              className="left-3 group-hover:opacity-100 opacity-0 transition duration-75 ease-in xl:w-12 xl:h-12" />
            <CarouselNext
              iconClassName="xl:!w-6 xl:!h-6"
              className="right-3 group-hover:opacity-100 opacity-0 transition duration-75 ease-in xl:w-12 xl:h-12" />
          </div>
        }
        <div className="flex absolute bottom-4 left-0 w-full items-center justify-center">
          <CarouselDots />
        </div>
      </Carousel>
    </div>
}